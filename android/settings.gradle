pluginManagement {
    repositories {
        gradlePluginPortal()
        google()
        mavenCentral()
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_PROJECT)
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        maven { url file("../node_modules/react-native/android") }
        maven { url file("../node_modules/jsc-android/dist") }
    }
}

rootProject.name = 'ReactNativeBridgeIos'
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
include ':app'
