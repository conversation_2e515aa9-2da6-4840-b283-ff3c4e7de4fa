diff --git a/node_modules/react-native/React/FBReactNativeSpec/FBReactNativeSpec/FBReactNativeSpec-generated.mm b/node_modules/react-native/React/FBReactNativeSpec/FBReactNativeSpec/FBReactNativeSpec-generated.mm
new file mode 100644
index 0000000..1e35b64
--- /dev/null
+++ b/node_modules/react-native/React/FBReactNativeSpec/FBReactNativeSpec/FBReactNativeSpec-generated.mm
@@ -0,0 +1,2096 @@
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateModuleObjCpp
+ *
+ * We create an umbrella header (and corresponding implementation) here since
+ * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
+ * must have a single output. More files => more genrule()s => slower builds.
+ */
+
+#import "FBReactNativeSpec.h"
+
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityInfoSpecJSI_isReduceMotionEnabled(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "isReduceMotionEnabled", @selector(isReduceMotionEnabled:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityInfoSpecJSI_isTouchExplorationEnabled(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "isTouchExplorationEnabled", @selector(isTouchExplorationEnabled:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityInfoSpecJSI_setAccessibilityFocus(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAccessibilityFocus", @selector(setAccessibilityFocus:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityInfoSpecJSI_announceForAccessibility(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "announceForAccessibility", @selector(announceForAccessibility:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityInfoSpecJSI_getRecommendedTimeoutMillis(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getRecommendedTimeoutMillis", @selector(getRecommendedTimeoutMillis:onSuccess:), args, count);
+    }
+
+    NativeAccessibilityInfoSpecJSI::NativeAccessibilityInfoSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["isReduceMotionEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoSpecJSI_isReduceMotionEnabled};
+        
+        
+        methodMap_["isTouchExplorationEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoSpecJSI_isTouchExplorationEnabled};
+        
+        
+        methodMap_["setAccessibilityFocus"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoSpecJSI_setAccessibilityFocus};
+        
+        
+        methodMap_["announceForAccessibility"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoSpecJSI_announceForAccessibility};
+        
+        
+        methodMap_["getRecommendedTimeoutMillis"] = MethodMetadata {2, __hostFunction_NativeAccessibilityInfoSpecJSI_getRecommendedTimeoutMillis};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeAccessibilityManager_SpecSetAccessibilityContentSizeMultipliersJSMultipliers)
++ (RCTManagedPointer *)JS_NativeAccessibilityManager_SpecSetAccessibilityContentSizeMultipliersJSMultipliers:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentBoldTextState(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getCurrentBoldTextState", @selector(getCurrentBoldTextState:onError:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentGrayscaleState(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getCurrentGrayscaleState", @selector(getCurrentGrayscaleState:onError:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentInvertColorsState(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getCurrentInvertColorsState", @selector(getCurrentInvertColorsState:onError:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentReduceMotionState(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getCurrentReduceMotionState", @selector(getCurrentReduceMotionState:onError:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentReduceTransparencyState(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getCurrentReduceTransparencyState", @selector(getCurrentReduceTransparencyState:onError:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentVoiceOverState(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getCurrentVoiceOverState", @selector(getCurrentVoiceOverState:onError:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_setAccessibilityContentSizeMultipliers(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAccessibilityContentSizeMultipliers", @selector(setAccessibilityContentSizeMultipliers:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_setAccessibilityFocus(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAccessibilityFocus", @selector(setAccessibilityFocus:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAccessibilityManagerSpecJSI_announceForAccessibility(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "announceForAccessibility", @selector(announceForAccessibility:), args, count);
+    }
+
+    NativeAccessibilityManagerSpecJSI::NativeAccessibilityManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getCurrentBoldTextState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentBoldTextState};
+        
+        
+        methodMap_["getCurrentGrayscaleState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentGrayscaleState};
+        
+        
+        methodMap_["getCurrentInvertColorsState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentInvertColorsState};
+        
+        
+        methodMap_["getCurrentReduceMotionState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentReduceMotionState};
+        
+        
+        methodMap_["getCurrentReduceTransparencyState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentReduceTransparencyState};
+        
+        
+        methodMap_["getCurrentVoiceOverState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerSpecJSI_getCurrentVoiceOverState};
+        
+        
+        methodMap_["setAccessibilityContentSizeMultipliers"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerSpecJSI_setAccessibilityContentSizeMultipliers};
+        setMethodArgConversionSelector(@"setAccessibilityContentSizeMultipliers", 0, @"JS_NativeAccessibilityManager_SpecSetAccessibilityContentSizeMultipliersJSMultipliers:");
+        
+        methodMap_["setAccessibilityFocus"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerSpecJSI_setAccessibilityFocus};
+        
+        
+        methodMap_["announceForAccessibility"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerSpecJSI_announceForAccessibility};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeActionSheetManager_SpecShowActionSheetWithOptionsOptions)
++ (RCTManagedPointer *)JS_NativeActionSheetManager_SpecShowActionSheetWithOptionsOptions:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions>(json);
+}
+@end
+@implementation RCTCxxConvert (NativeActionSheetManager_SpecShowShareActionSheetWithOptionsOptions)
++ (RCTManagedPointer *)JS_NativeActionSheetManager_SpecShowShareActionSheetWithOptionsOptions:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeActionSheetManagerSpecJSI_showActionSheetWithOptions(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "showActionSheetWithOptions", @selector(showActionSheetWithOptions:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeActionSheetManagerSpecJSI_showShareActionSheetWithOptions(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "showShareActionSheetWithOptions", @selector(showShareActionSheetWithOptions:failureCallback:successCallback:), args, count);
+    }
+
+    NativeActionSheetManagerSpecJSI::NativeActionSheetManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["showActionSheetWithOptions"] = MethodMetadata {2, __hostFunction_NativeActionSheetManagerSpecJSI_showActionSheetWithOptions};
+        setMethodArgConversionSelector(@"showActionSheetWithOptions", 0, @"JS_NativeActionSheetManager_SpecShowActionSheetWithOptionsOptions:");
+        
+        methodMap_["showShareActionSheetWithOptions"] = MethodMetadata {3, __hostFunction_NativeActionSheetManagerSpecJSI_showShareActionSheetWithOptions};
+        setMethodArgConversionSelector(@"showShareActionSheetWithOptions", 0, @"JS_NativeActionSheetManager_SpecShowShareActionSheetWithOptionsOptions:");
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeAlertManager_Args)
++ (RCTManagedPointer *)JS_NativeAlertManager_Args:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeAlertManager::Args>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAlertManagerSpecJSI_alertWithArgs(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "alertWithArgs", @selector(alertWithArgs:callback:), args, count);
+    }
+
+    NativeAlertManagerSpecJSI::NativeAlertManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["alertWithArgs"] = MethodMetadata {2, __hostFunction_NativeAlertManagerSpecJSI_alertWithArgs};
+        setMethodArgConversionSelector(@"alertWithArgs", 0, @"JS_NativeAlertManager_Args:");
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeAnimatedModule_EventMapping)
++ (RCTManagedPointer *)JS_NativeAnimatedModule_EventMapping:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeAnimatedModule::EventMapping>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_startOperationBatch(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startOperationBatch", @selector(startOperationBatch), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_finishOperationBatch(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "finishOperationBatch", @selector(finishOperationBatch), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_createAnimatedNode(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "createAnimatedNode", @selector(createAnimatedNode:config:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_getValue(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getValue", @selector(getValue:saveValueCallback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_startListeningToAnimatedNodeValue(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startListeningToAnimatedNodeValue", @selector(startListeningToAnimatedNodeValue:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_stopListeningToAnimatedNodeValue(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "stopListeningToAnimatedNodeValue", @selector(stopListeningToAnimatedNodeValue:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_connectAnimatedNodes(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "connectAnimatedNodes", @selector(connectAnimatedNodes:childTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_disconnectAnimatedNodes(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "disconnectAnimatedNodes", @selector(disconnectAnimatedNodes:childTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_startAnimatingNode(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startAnimatingNode", @selector(startAnimatingNode:nodeTag:config:endCallback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_stopAnimation(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "stopAnimation", @selector(stopAnimation:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_setAnimatedNodeValue(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAnimatedNodeValue", @selector(setAnimatedNodeValue:value:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_setAnimatedNodeOffset(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAnimatedNodeOffset", @selector(setAnimatedNodeOffset:offset:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_flattenAnimatedNodeOffset(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "flattenAnimatedNodeOffset", @selector(flattenAnimatedNodeOffset:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_extractAnimatedNodeOffset(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "extractAnimatedNodeOffset", @selector(extractAnimatedNodeOffset:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_connectAnimatedNodeToView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "connectAnimatedNodeToView", @selector(connectAnimatedNodeToView:viewTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_disconnectAnimatedNodeFromView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "disconnectAnimatedNodeFromView", @selector(disconnectAnimatedNodeFromView:viewTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_restoreDefaultValues(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "restoreDefaultValues", @selector(restoreDefaultValues:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_dropAnimatedNode(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "dropAnimatedNode", @selector(dropAnimatedNode:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_addAnimatedEventToView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addAnimatedEventToView", @selector(addAnimatedEventToView:eventName:eventMapping:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_removeAnimatedEventFromView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeAnimatedEventFromView", @selector(removeAnimatedEventFromView:eventName:animatedNodeTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedModuleSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativeAnimatedModuleSpecJSI::NativeAnimatedModuleSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["startOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedModuleSpecJSI_startOperationBatch};
+        
+        
+        methodMap_["finishOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedModuleSpecJSI_finishOperationBatch};
+        
+        
+        methodMap_["createAnimatedNode"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleSpecJSI_createAnimatedNode};
+        
+        
+        methodMap_["getValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleSpecJSI_getValue};
+        
+        
+        methodMap_["startListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_startListeningToAnimatedNodeValue};
+        
+        
+        methodMap_["stopListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_stopListeningToAnimatedNodeValue};
+        
+        
+        methodMap_["connectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleSpecJSI_connectAnimatedNodes};
+        
+        
+        methodMap_["disconnectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleSpecJSI_disconnectAnimatedNodes};
+        
+        
+        methodMap_["startAnimatingNode"] = MethodMetadata {4, __hostFunction_NativeAnimatedModuleSpecJSI_startAnimatingNode};
+        
+        
+        methodMap_["stopAnimation"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_stopAnimation};
+        
+        
+        methodMap_["setAnimatedNodeValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleSpecJSI_setAnimatedNodeValue};
+        
+        
+        methodMap_["setAnimatedNodeOffset"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleSpecJSI_setAnimatedNodeOffset};
+        
+        
+        methodMap_["flattenAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_flattenAnimatedNodeOffset};
+        
+        
+        methodMap_["extractAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_extractAnimatedNodeOffset};
+        
+        
+        methodMap_["connectAnimatedNodeToView"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleSpecJSI_connectAnimatedNodeToView};
+        
+        
+        methodMap_["disconnectAnimatedNodeFromView"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleSpecJSI_disconnectAnimatedNodeFromView};
+        
+        
+        methodMap_["restoreDefaultValues"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_restoreDefaultValues};
+        
+        
+        methodMap_["dropAnimatedNode"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_dropAnimatedNode};
+        
+        
+        methodMap_["addAnimatedEventToView"] = MethodMetadata {3, __hostFunction_NativeAnimatedModuleSpecJSI_addAnimatedEventToView};
+        setMethodArgConversionSelector(@"addAnimatedEventToView", 2, @"JS_NativeAnimatedModule_EventMapping:");
+        
+        methodMap_["removeAnimatedEventFromView"] = MethodMetadata {3, __hostFunction_NativeAnimatedModuleSpecJSI_removeAnimatedEventFromView};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeAnimatedTurboModule_EventMapping)
++ (RCTManagedPointer *)JS_NativeAnimatedTurboModule_EventMapping:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeAnimatedTurboModule::EventMapping>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_startOperationBatch(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startOperationBatch", @selector(startOperationBatch), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_finishOperationBatch(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "finishOperationBatch", @selector(finishOperationBatch), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_createAnimatedNode(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "createAnimatedNode", @selector(createAnimatedNode:config:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_getValue(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getValue", @selector(getValue:saveValueCallback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_startListeningToAnimatedNodeValue(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startListeningToAnimatedNodeValue", @selector(startListeningToAnimatedNodeValue:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_stopListeningToAnimatedNodeValue(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "stopListeningToAnimatedNodeValue", @selector(stopListeningToAnimatedNodeValue:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_connectAnimatedNodes(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "connectAnimatedNodes", @selector(connectAnimatedNodes:childTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_disconnectAnimatedNodes(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "disconnectAnimatedNodes", @selector(disconnectAnimatedNodes:childTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_startAnimatingNode(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startAnimatingNode", @selector(startAnimatingNode:nodeTag:config:endCallback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_stopAnimation(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "stopAnimation", @selector(stopAnimation:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_setAnimatedNodeValue(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAnimatedNodeValue", @selector(setAnimatedNodeValue:value:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_setAnimatedNodeOffset(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setAnimatedNodeOffset", @selector(setAnimatedNodeOffset:offset:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_flattenAnimatedNodeOffset(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "flattenAnimatedNodeOffset", @selector(flattenAnimatedNodeOffset:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_extractAnimatedNodeOffset(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "extractAnimatedNodeOffset", @selector(extractAnimatedNodeOffset:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_connectAnimatedNodeToView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "connectAnimatedNodeToView", @selector(connectAnimatedNodeToView:viewTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_disconnectAnimatedNodeFromView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "disconnectAnimatedNodeFromView", @selector(disconnectAnimatedNodeFromView:viewTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_restoreDefaultValues(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "restoreDefaultValues", @selector(restoreDefaultValues:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_dropAnimatedNode(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "dropAnimatedNode", @selector(dropAnimatedNode:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_addAnimatedEventToView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addAnimatedEventToView", @selector(addAnimatedEventToView:eventName:eventMapping:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_removeAnimatedEventFromView(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeAnimatedEventFromView", @selector(removeAnimatedEventFromView:eventName:animatedNodeTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimatedTurboModuleSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativeAnimatedTurboModuleSpecJSI::NativeAnimatedTurboModuleSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["startOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedTurboModuleSpecJSI_startOperationBatch};
+        
+        
+        methodMap_["finishOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedTurboModuleSpecJSI_finishOperationBatch};
+        
+        
+        methodMap_["createAnimatedNode"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleSpecJSI_createAnimatedNode};
+        
+        
+        methodMap_["getValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleSpecJSI_getValue};
+        
+        
+        methodMap_["startListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_startListeningToAnimatedNodeValue};
+        
+        
+        methodMap_["stopListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_stopListeningToAnimatedNodeValue};
+        
+        
+        methodMap_["connectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleSpecJSI_connectAnimatedNodes};
+        
+        
+        methodMap_["disconnectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleSpecJSI_disconnectAnimatedNodes};
+        
+        
+        methodMap_["startAnimatingNode"] = MethodMetadata {4, __hostFunction_NativeAnimatedTurboModuleSpecJSI_startAnimatingNode};
+        
+        
+        methodMap_["stopAnimation"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_stopAnimation};
+        
+        
+        methodMap_["setAnimatedNodeValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleSpecJSI_setAnimatedNodeValue};
+        
+        
+        methodMap_["setAnimatedNodeOffset"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleSpecJSI_setAnimatedNodeOffset};
+        
+        
+        methodMap_["flattenAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_flattenAnimatedNodeOffset};
+        
+        
+        methodMap_["extractAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_extractAnimatedNodeOffset};
+        
+        
+        methodMap_["connectAnimatedNodeToView"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleSpecJSI_connectAnimatedNodeToView};
+        
+        
+        methodMap_["disconnectAnimatedNodeFromView"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleSpecJSI_disconnectAnimatedNodeFromView};
+        
+        
+        methodMap_["restoreDefaultValues"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_restoreDefaultValues};
+        
+        
+        methodMap_["dropAnimatedNode"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_dropAnimatedNode};
+        
+        
+        methodMap_["addAnimatedEventToView"] = MethodMetadata {3, __hostFunction_NativeAnimatedTurboModuleSpecJSI_addAnimatedEventToView};
+        setMethodArgConversionSelector(@"addAnimatedEventToView", 2, @"JS_NativeAnimatedTurboModule_EventMapping:");
+        
+        methodMap_["removeAnimatedEventFromView"] = MethodMetadata {3, __hostFunction_NativeAnimatedTurboModuleSpecJSI_removeAnimatedEventFromView};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAnimationsDebugModuleSpecJSI_startRecordingFps(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startRecordingFps", @selector(startRecordingFps), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAnimationsDebugModuleSpecJSI_stopRecordingFps(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "stopRecordingFps", @selector(stopRecordingFps:), args, count);
+    }
+
+    NativeAnimationsDebugModuleSpecJSI::NativeAnimationsDebugModuleSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["startRecordingFps"] = MethodMetadata {0, __hostFunction_NativeAnimationsDebugModuleSpecJSI_startRecordingFps};
+        
+        
+        methodMap_["stopRecordingFps"] = MethodMetadata {1, __hostFunction_NativeAnimationsDebugModuleSpecJSI_stopRecordingFps};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAppStateSpecJSI_getCurrentAppState(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getCurrentAppState", @selector(getCurrentAppState:error:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAppStateSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAppStateSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAppStateSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativeAppStateSpecJSI::NativeAppStateSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getCurrentAppState"] = MethodMetadata {2, __hostFunction_NativeAppStateSpecJSI_getCurrentAppState};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAppStateSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAppStateSpecJSI_removeListeners};
+        
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeAppStateSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAppearanceSpecJSI_getColorScheme(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, StringKind, "getColorScheme", @selector(getColorScheme), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAppearanceSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAppearanceSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativeAppearanceSpecJSI::NativeAppearanceSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getColorScheme"] = MethodMetadata {0, __hostFunction_NativeAppearanceSpecJSI_getColorScheme};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAppearanceSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAppearanceSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAsyncLocalStorageSpecJSI_multiGet(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiGet", @selector(multiGet:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncLocalStorageSpecJSI_multiSet(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiSet", @selector(multiSet:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncLocalStorageSpecJSI_multiMerge(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiMerge", @selector(multiMerge:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncLocalStorageSpecJSI_multiRemove(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiRemove", @selector(multiRemove:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncLocalStorageSpecJSI_clear(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "clear", @selector(clear:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncLocalStorageSpecJSI_getAllKeys(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getAllKeys", @selector(getAllKeys:), args, count);
+    }
+
+    NativeAsyncLocalStorageSpecJSI::NativeAsyncLocalStorageSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["multiGet"] = MethodMetadata {2, __hostFunction_NativeAsyncLocalStorageSpecJSI_multiGet};
+        
+        
+        methodMap_["multiSet"] = MethodMetadata {2, __hostFunction_NativeAsyncLocalStorageSpecJSI_multiSet};
+        
+        
+        methodMap_["multiMerge"] = MethodMetadata {2, __hostFunction_NativeAsyncLocalStorageSpecJSI_multiMerge};
+        
+        
+        methodMap_["multiRemove"] = MethodMetadata {2, __hostFunction_NativeAsyncLocalStorageSpecJSI_multiRemove};
+        
+        
+        methodMap_["clear"] = MethodMetadata {1, __hostFunction_NativeAsyncLocalStorageSpecJSI_clear};
+        
+        
+        methodMap_["getAllKeys"] = MethodMetadata {1, __hostFunction_NativeAsyncLocalStorageSpecJSI_getAllKeys};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_multiGet(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiGet", @selector(multiGet:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_multiSet(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiSet", @selector(multiSet:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_multiMerge(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiMerge", @selector(multiMerge:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_multiRemove(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "multiRemove", @selector(multiRemove:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_clear(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "clear", @selector(clear:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_getAllKeys(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getAllKeys", @selector(getAllKeys:), args, count);
+    }
+
+    NativeAsyncSQLiteDBStorageSpecJSI::NativeAsyncSQLiteDBStorageSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["multiGet"] = MethodMetadata {2, __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_multiGet};
+        
+        
+        methodMap_["multiSet"] = MethodMetadata {2, __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_multiSet};
+        
+        
+        methodMap_["multiMerge"] = MethodMetadata {2, __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_multiMerge};
+        
+        
+        methodMap_["multiRemove"] = MethodMetadata {2, __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_multiRemove};
+        
+        
+        methodMap_["clear"] = MethodMetadata {1, __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_clear};
+        
+        
+        methodMap_["getAllKeys"] = MethodMetadata {1, __hostFunction_NativeAsyncSQLiteDBStorageSpecJSI_getAllKeys};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeBlobModuleSpecJSI_addNetworkingHandler(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addNetworkingHandler", @selector(addNetworkingHandler), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeBlobModuleSpecJSI_addWebSocketHandler(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addWebSocketHandler", @selector(addWebSocketHandler:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeBlobModuleSpecJSI_removeWebSocketHandler(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeWebSocketHandler", @selector(removeWebSocketHandler:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeBlobModuleSpecJSI_sendOverSocket(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "sendOverSocket", @selector(sendOverSocket:socketID:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeBlobModuleSpecJSI_createFromParts(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "createFromParts", @selector(createFromParts:withId:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeBlobModuleSpecJSI_release(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "release", @selector(release:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeBlobModuleSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativeBlobModuleSpecJSI::NativeBlobModuleSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["addNetworkingHandler"] = MethodMetadata {0, __hostFunction_NativeBlobModuleSpecJSI_addNetworkingHandler};
+        
+        
+        methodMap_["addWebSocketHandler"] = MethodMetadata {1, __hostFunction_NativeBlobModuleSpecJSI_addWebSocketHandler};
+        
+        
+        methodMap_["removeWebSocketHandler"] = MethodMetadata {1, __hostFunction_NativeBlobModuleSpecJSI_removeWebSocketHandler};
+        
+        
+        methodMap_["sendOverSocket"] = MethodMetadata {2, __hostFunction_NativeBlobModuleSpecJSI_sendOverSocket};
+        
+        
+        methodMap_["createFromParts"] = MethodMetadata {2, __hostFunction_NativeBlobModuleSpecJSI_createFromParts};
+        
+        
+        methodMap_["release"] = MethodMetadata {1, __hostFunction_NativeBlobModuleSpecJSI_release};
+        
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeBlobModuleSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeBugReportingSpecJSI_startReportAProblemFlow(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "startReportAProblemFlow", @selector(startReportAProblemFlow), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeBugReportingSpecJSI_setExtraData(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setExtraData", @selector(setExtraData:extraFiles:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeBugReportingSpecJSI_setCategoryID(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setCategoryID", @selector(setCategoryID:), args, count);
+    }
+
+    NativeBugReportingSpecJSI::NativeBugReportingSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["startReportAProblemFlow"] = MethodMetadata {0, __hostFunction_NativeBugReportingSpecJSI_startReportAProblemFlow};
+        
+        
+        methodMap_["setExtraData"] = MethodMetadata {2, __hostFunction_NativeBugReportingSpecJSI_setExtraData};
+        
+        
+        methodMap_["setCategoryID"] = MethodMetadata {1, __hostFunction_NativeBugReportingSpecJSI_setCategoryID};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeClipboardSpecJSI_getString(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getString", @selector(getString:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeClipboardSpecJSI_setString(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setString", @selector(setString:), args, count);
+    }
+
+    NativeClipboardSpecJSI::NativeClipboardSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getString"] = MethodMetadata {0, __hostFunction_NativeClipboardSpecJSI_getString};
+        
+        
+        methodMap_["setString"] = MethodMetadata {1, __hostFunction_NativeClipboardSpecJSI_setString};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeDevLoadingViewSpecJSI_showMessage(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "showMessage", @selector(showMessage:withColor:withBackgroundColor:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevLoadingViewSpecJSI_hide(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "hide", @selector(hide), args, count);
+    }
+
+    NativeDevLoadingViewSpecJSI::NativeDevLoadingViewSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["showMessage"] = MethodMetadata {3, __hostFunction_NativeDevLoadingViewSpecJSI_showMessage};
+        
+        
+        methodMap_["hide"] = MethodMetadata {0, __hostFunction_NativeDevLoadingViewSpecJSI_hide};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeDevMenuSpecJSI_show(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "show", @selector(show), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevMenuSpecJSI_reload(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "reload", @selector(reload), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevMenuSpecJSI_debugRemotely(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "debugRemotely", @selector(debugRemotely:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevMenuSpecJSI_setProfilingEnabled(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setProfilingEnabled", @selector(setProfilingEnabled:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevMenuSpecJSI_setHotLoadingEnabled(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setHotLoadingEnabled", @selector(setHotLoadingEnabled:), args, count);
+    }
+
+    NativeDevMenuSpecJSI::NativeDevMenuSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["show"] = MethodMetadata {0, __hostFunction_NativeDevMenuSpecJSI_show};
+        
+        
+        methodMap_["reload"] = MethodMetadata {0, __hostFunction_NativeDevMenuSpecJSI_reload};
+        
+        
+        methodMap_["debugRemotely"] = MethodMetadata {1, __hostFunction_NativeDevMenuSpecJSI_debugRemotely};
+        
+        
+        methodMap_["setProfilingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevMenuSpecJSI_setProfilingEnabled};
+        
+        
+        methodMap_["setHotLoadingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevMenuSpecJSI_setHotLoadingEnabled};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_reload(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "reload", @selector(reload), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_reloadWithReason(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "reloadWithReason", @selector(reloadWithReason:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_onFastRefresh(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "onFastRefresh", @selector(onFastRefresh), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_setHotLoadingEnabled(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setHotLoadingEnabled", @selector(setHotLoadingEnabled:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_setIsDebuggingRemotely(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setIsDebuggingRemotely", @selector(setIsDebuggingRemotely:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_setProfilingEnabled(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setProfilingEnabled", @selector(setProfilingEnabled:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_toggleElementInspector(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "toggleElementInspector", @selector(toggleElementInspector), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_addMenuItem(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addMenuItem", @selector(addMenuItem:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeDevSettingsSpecJSI_setIsShakeToShowDevMenuEnabled(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setIsShakeToShowDevMenuEnabled", @selector(setIsShakeToShowDevMenuEnabled:), args, count);
+    }
+
+    NativeDevSettingsSpecJSI::NativeDevSettingsSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["reload"] = MethodMetadata {0, __hostFunction_NativeDevSettingsSpecJSI_reload};
+        
+        
+        methodMap_["reloadWithReason"] = MethodMetadata {1, __hostFunction_NativeDevSettingsSpecJSI_reloadWithReason};
+        
+        
+        methodMap_["onFastRefresh"] = MethodMetadata {0, __hostFunction_NativeDevSettingsSpecJSI_onFastRefresh};
+        
+        
+        methodMap_["setHotLoadingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsSpecJSI_setHotLoadingEnabled};
+        
+        
+        methodMap_["setIsDebuggingRemotely"] = MethodMetadata {1, __hostFunction_NativeDevSettingsSpecJSI_setIsDebuggingRemotely};
+        
+        
+        methodMap_["setProfilingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsSpecJSI_setProfilingEnabled};
+        
+        
+        methodMap_["toggleElementInspector"] = MethodMetadata {0, __hostFunction_NativeDevSettingsSpecJSI_toggleElementInspector};
+        
+        
+        methodMap_["addMenuItem"] = MethodMetadata {1, __hostFunction_NativeDevSettingsSpecJSI_addMenuItem};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeDevSettingsSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeDevSettingsSpecJSI_removeListeners};
+        
+        
+        methodMap_["setIsShakeToShowDevMenuEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsSpecJSI_setIsShakeToShowDevMenuEnabled};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeDevSplitBundleLoaderSpecJSI_loadBundle(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "loadBundle", @selector(loadBundle:resolve:reject:), args, count);
+    }
+
+    NativeDevSplitBundleLoaderSpecJSI::NativeDevSplitBundleLoaderSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["loadBundle"] = MethodMetadata {1, __hostFunction_NativeDevSplitBundleLoaderSpecJSI_loadBundle};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeDeviceEventManagerSpecJSI_invokeDefaultBackPressHandler(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "invokeDefaultBackPressHandler", @selector(invokeDefaultBackPressHandler), args, count);
+    }
+
+    NativeDeviceEventManagerSpecJSI::NativeDeviceEventManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["invokeDefaultBackPressHandler"] = MethodMetadata {0, __hostFunction_NativeDeviceEventManagerSpecJSI_invokeDefaultBackPressHandler};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeDeviceInfoSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativeDeviceInfoSpecJSI::NativeDeviceInfoSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeDeviceInfoSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeExceptionsManager_StackFrame)
++ (RCTManagedPointer *)JS_NativeExceptionsManager_StackFrame:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeExceptionsManager::StackFrame>(json);
+}
+@end
+@implementation RCTCxxConvert (NativeExceptionsManager_ExceptionData)
++ (RCTManagedPointer *)JS_NativeExceptionsManager_ExceptionData:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeExceptionsManager::ExceptionData>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeExceptionsManagerSpecJSI_reportFatalException(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "reportFatalException", @selector(reportFatalException:stack:exceptionId:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeExceptionsManagerSpecJSI_reportSoftException(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "reportSoftException", @selector(reportSoftException:stack:exceptionId:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeExceptionsManagerSpecJSI_reportException(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "reportException", @selector(reportException:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeExceptionsManagerSpecJSI_updateExceptionMessage(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "updateExceptionMessage", @selector(updateExceptionMessage:stack:exceptionId:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeExceptionsManagerSpecJSI_dismissRedbox(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "dismissRedbox", @selector(dismissRedbox), args, count);
+    }
+
+    NativeExceptionsManagerSpecJSI::NativeExceptionsManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["reportFatalException"] = MethodMetadata {3, __hostFunction_NativeExceptionsManagerSpecJSI_reportFatalException};
+        
+        
+        methodMap_["reportSoftException"] = MethodMetadata {3, __hostFunction_NativeExceptionsManagerSpecJSI_reportSoftException};
+        
+        
+        methodMap_["reportException"] = MethodMetadata {1, __hostFunction_NativeExceptionsManagerSpecJSI_reportException};
+        setMethodArgConversionSelector(@"reportException", 0, @"JS_NativeExceptionsManager_ExceptionData:");
+        
+        methodMap_["updateExceptionMessage"] = MethodMetadata {3, __hostFunction_NativeExceptionsManagerSpecJSI_updateExceptionMessage};
+        
+        
+        methodMap_["dismissRedbox"] = MethodMetadata {0, __hostFunction_NativeExceptionsManagerSpecJSI_dismissRedbox};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeFileReaderModuleSpecJSI_readAsDataURL(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "readAsDataURL", @selector(readAsDataURL:resolve:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeFileReaderModuleSpecJSI_readAsText(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "readAsText", @selector(readAsText:encoding:resolve:reject:), args, count);
+    }
+
+    NativeFileReaderModuleSpecJSI::NativeFileReaderModuleSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["readAsDataURL"] = MethodMetadata {1, __hostFunction_NativeFileReaderModuleSpecJSI_readAsDataURL};
+        
+        
+        methodMap_["readAsText"] = MethodMetadata {2, __hostFunction_NativeFileReaderModuleSpecJSI_readAsText};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeFrameRateLogger_SpecSetGlobalOptionsOptions)
++ (RCTManagedPointer *)JS_NativeFrameRateLogger_SpecSetGlobalOptionsOptions:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeFrameRateLogger::SpecSetGlobalOptionsOptions>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeFrameRateLoggerSpecJSI_setGlobalOptions(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setGlobalOptions", @selector(setGlobalOptions:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeFrameRateLoggerSpecJSI_setContext(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setContext", @selector(setContext:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeFrameRateLoggerSpecJSI_beginScroll(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "beginScroll", @selector(beginScroll), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeFrameRateLoggerSpecJSI_endScroll(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "endScroll", @selector(endScroll), args, count);
+    }
+
+    NativeFrameRateLoggerSpecJSI::NativeFrameRateLoggerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["setGlobalOptions"] = MethodMetadata {1, __hostFunction_NativeFrameRateLoggerSpecJSI_setGlobalOptions};
+        setMethodArgConversionSelector(@"setGlobalOptions", 0, @"JS_NativeFrameRateLogger_SpecSetGlobalOptionsOptions:");
+        
+        methodMap_["setContext"] = MethodMetadata {1, __hostFunction_NativeFrameRateLoggerSpecJSI_setContext};
+        
+        
+        methodMap_["beginScroll"] = MethodMetadata {0, __hostFunction_NativeFrameRateLoggerSpecJSI_beginScroll};
+        
+        
+        methodMap_["endScroll"] = MethodMetadata {0, __hostFunction_NativeFrameRateLoggerSpecJSI_endScroll};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeHeadlessJsTaskSupportSpecJSI_notifyTaskFinished(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "notifyTaskFinished", @selector(notifyTaskFinished:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeHeadlessJsTaskSupportSpecJSI_notifyTaskRetry(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "notifyTaskRetry", @selector(notifyTaskRetry:resolve:reject:), args, count);
+    }
+
+    NativeHeadlessJsTaskSupportSpecJSI::NativeHeadlessJsTaskSupportSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["notifyTaskFinished"] = MethodMetadata {1, __hostFunction_NativeHeadlessJsTaskSupportSpecJSI_notifyTaskFinished};
+        
+        
+        methodMap_["notifyTaskRetry"] = MethodMetadata {1, __hostFunction_NativeHeadlessJsTaskSupportSpecJSI_notifyTaskRetry};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeI18nManagerSpecJSI_allowRTL(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "allowRTL", @selector(allowRTL:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeI18nManagerSpecJSI_forceRTL(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "forceRTL", @selector(forceRTL:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeI18nManagerSpecJSI_swapLeftAndRightInRTL(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "swapLeftAndRightInRTL", @selector(swapLeftAndRightInRTL:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeI18nManagerSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativeI18nManagerSpecJSI::NativeI18nManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["allowRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerSpecJSI_allowRTL};
+        
+        
+        methodMap_["forceRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerSpecJSI_forceRTL};
+        
+        
+        methodMap_["swapLeftAndRightInRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerSpecJSI_swapLeftAndRightInRTL};
+        
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeI18nManagerSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeImageEditor_OptionsOffset)
++ (RCTManagedPointer *)JS_NativeImageEditor_OptionsOffset:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeImageEditor::OptionsOffset>(json);
+}
+@end
+@implementation RCTCxxConvert (NativeImageEditor_OptionsSize)
++ (RCTManagedPointer *)JS_NativeImageEditor_OptionsSize:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeImageEditor::OptionsSize>(json);
+}
+@end
+@implementation RCTCxxConvert (NativeImageEditor_OptionsDisplaySize)
++ (RCTManagedPointer *)JS_NativeImageEditor_OptionsDisplaySize:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeImageEditor::OptionsDisplaySize>(json);
+}
+@end
+@implementation RCTCxxConvert (NativeImageEditor_Options)
++ (RCTManagedPointer *)JS_NativeImageEditor_Options:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeImageEditor::Options>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeImageEditorSpecJSI_cropImage(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "cropImage", @selector(cropImage:cropData:successCallback:errorCallback:), args, count);
+    }
+
+    NativeImageEditorSpecJSI::NativeImageEditorSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["cropImage"] = MethodMetadata {4, __hostFunction_NativeImageEditorSpecJSI_cropImage};
+        setMethodArgConversionSelector(@"cropImage", 1, @"JS_NativeImageEditor_Options:");
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeImageLoaderIOSSpecJSI_getSize(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getSize", @selector(getSize:resolve:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImageLoaderIOSSpecJSI_getSizeWithHeaders(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getSizeWithHeaders", @selector(getSizeWithHeaders:headers:resolve:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImageLoaderIOSSpecJSI_prefetchImage(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "prefetchImage", @selector(prefetchImage:resolve:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImageLoaderIOSSpecJSI_prefetchImageWithMetadata(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "prefetchImageWithMetadata", @selector(prefetchImageWithMetadata:queryRootName:rootTag:resolve:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImageLoaderIOSSpecJSI_queryCache(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "queryCache", @selector(queryCache:resolve:reject:), args, count);
+    }
+
+    NativeImageLoaderIOSSpecJSI::NativeImageLoaderIOSSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getSize"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSSpecJSI_getSize};
+        
+        
+        methodMap_["getSizeWithHeaders"] = MethodMetadata {2, __hostFunction_NativeImageLoaderIOSSpecJSI_getSizeWithHeaders};
+        
+        
+        methodMap_["prefetchImage"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSSpecJSI_prefetchImage};
+        
+        
+        methodMap_["prefetchImageWithMetadata"] = MethodMetadata {3, __hostFunction_NativeImageLoaderIOSSpecJSI_prefetchImageWithMetadata};
+        
+        
+        methodMap_["queryCache"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSSpecJSI_queryCache};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeImagePickerIOS_SpecOpenCameraDialogConfig)
++ (RCTManagedPointer *)JS_NativeImagePickerIOS_SpecOpenCameraDialogConfig:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeImagePickerIOS::SpecOpenCameraDialogConfig>(json);
+}
+@end
+@implementation RCTCxxConvert (NativeImagePickerIOS_SpecOpenSelectDialogConfig)
++ (RCTManagedPointer *)JS_NativeImagePickerIOS_SpecOpenSelectDialogConfig:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeImagePickerIOS::SpecOpenSelectDialogConfig>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeImagePickerIOSSpecJSI_canRecordVideos(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "canRecordVideos", @selector(canRecordVideos:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImagePickerIOSSpecJSI_canUseCamera(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "canUseCamera", @selector(canUseCamera:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImagePickerIOSSpecJSI_openCameraDialog(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "openCameraDialog", @selector(openCameraDialog:successCallback:cancelCallback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImagePickerIOSSpecJSI_openSelectDialog(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "openSelectDialog", @selector(openSelectDialog:successCallback:cancelCallback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImagePickerIOSSpecJSI_clearAllPendingVideos(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "clearAllPendingVideos", @selector(clearAllPendingVideos), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImagePickerIOSSpecJSI_removePendingVideo(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removePendingVideo", @selector(removePendingVideo:), args, count);
+    }
+
+    NativeImagePickerIOSSpecJSI::NativeImagePickerIOSSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["canRecordVideos"] = MethodMetadata {1, __hostFunction_NativeImagePickerIOSSpecJSI_canRecordVideos};
+        
+        
+        methodMap_["canUseCamera"] = MethodMetadata {1, __hostFunction_NativeImagePickerIOSSpecJSI_canUseCamera};
+        
+        
+        methodMap_["openCameraDialog"] = MethodMetadata {3, __hostFunction_NativeImagePickerIOSSpecJSI_openCameraDialog};
+        setMethodArgConversionSelector(@"openCameraDialog", 0, @"JS_NativeImagePickerIOS_SpecOpenCameraDialogConfig:");
+        
+        methodMap_["openSelectDialog"] = MethodMetadata {3, __hostFunction_NativeImagePickerIOSSpecJSI_openSelectDialog};
+        setMethodArgConversionSelector(@"openSelectDialog", 0, @"JS_NativeImagePickerIOS_SpecOpenSelectDialogConfig:");
+        
+        methodMap_["clearAllPendingVideos"] = MethodMetadata {0, __hostFunction_NativeImagePickerIOSSpecJSI_clearAllPendingVideos};
+        
+        
+        methodMap_["removePendingVideo"] = MethodMetadata {1, __hostFunction_NativeImagePickerIOSSpecJSI_removePendingVideo};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeImageStoreIOSSpecJSI_getBase64ForTag(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getBase64ForTag", @selector(getBase64ForTag:successCallback:errorCallback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImageStoreIOSSpecJSI_hasImageForTag(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "hasImageForTag", @selector(hasImageForTag:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImageStoreIOSSpecJSI_removeImageForTag(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeImageForTag", @selector(removeImageForTag:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeImageStoreIOSSpecJSI_addImageFromBase64(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addImageFromBase64", @selector(addImageFromBase64:successCallback:errorCallback:), args, count);
+    }
+
+    NativeImageStoreIOSSpecJSI::NativeImageStoreIOSSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getBase64ForTag"] = MethodMetadata {3, __hostFunction_NativeImageStoreIOSSpecJSI_getBase64ForTag};
+        
+        
+        methodMap_["hasImageForTag"] = MethodMetadata {2, __hostFunction_NativeImageStoreIOSSpecJSI_hasImageForTag};
+        
+        
+        methodMap_["removeImageForTag"] = MethodMetadata {1, __hostFunction_NativeImageStoreIOSSpecJSI_removeImageForTag};
+        
+        
+        methodMap_["addImageFromBase64"] = MethodMetadata {3, __hostFunction_NativeImageStoreIOSSpecJSI_addImageFromBase64};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeJSCHeapCaptureSpecJSI_captureComplete(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "captureComplete", @selector(captureComplete:error:), args, count);
+    }
+
+    NativeJSCHeapCaptureSpecJSI::NativeJSCHeapCaptureSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["captureComplete"] = MethodMetadata {2, __hostFunction_NativeJSCHeapCaptureSpecJSI_captureComplete};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeJSCSamplingProfilerSpecJSI_operationComplete(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "operationComplete", @selector(operationComplete:result:error:), args, count);
+    }
+
+    NativeJSCSamplingProfilerSpecJSI::NativeJSCSamplingProfilerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["operationComplete"] = MethodMetadata {3, __hostFunction_NativeJSCSamplingProfilerSpecJSI_operationComplete};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeJSDevSupportSpecJSI_onSuccess(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "onSuccess", @selector(onSuccess:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeJSDevSupportSpecJSI_onFailure(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "onFailure", @selector(onFailure:error:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeJSDevSupportSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativeJSDevSupportSpecJSI::NativeJSDevSupportSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["onSuccess"] = MethodMetadata {1, __hostFunction_NativeJSDevSupportSpecJSI_onSuccess};
+        
+        
+        methodMap_["onFailure"] = MethodMetadata {2, __hostFunction_NativeJSDevSupportSpecJSI_onFailure};
+        
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeJSDevSupportSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeKeyboardObserverSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeKeyboardObserverSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativeKeyboardObserverSpecJSI::NativeKeyboardObserverSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeKeyboardObserverSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeKeyboardObserverSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeLinkingManagerSpecJSI_getInitialURL(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getInitialURL", @selector(getInitialURL:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeLinkingManagerSpecJSI_canOpenURL(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "canOpenURL", @selector(canOpenURL:resolve:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeLinkingManagerSpecJSI_openURL(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "openURL", @selector(openURL:resolve:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeLinkingManagerSpecJSI_openSettings(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "openSettings", @selector(openSettings:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeLinkingManagerSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeLinkingManagerSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativeLinkingManagerSpecJSI::NativeLinkingManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getInitialURL"] = MethodMetadata {0, __hostFunction_NativeLinkingManagerSpecJSI_getInitialURL};
+        
+        
+        methodMap_["canOpenURL"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerSpecJSI_canOpenURL};
+        
+        
+        methodMap_["openURL"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerSpecJSI_openURL};
+        
+        
+        methodMap_["openSettings"] = MethodMetadata {0, __hostFunction_NativeLinkingManagerSpecJSI_openSettings};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeLogBoxSpecJSI_show(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "show", @selector(show), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeLogBoxSpecJSI_hide(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "hide", @selector(hide), args, count);
+    }
+
+    NativeLogBoxSpecJSI::NativeLogBoxSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["show"] = MethodMetadata {0, __hostFunction_NativeLogBoxSpecJSI_show};
+        
+        
+        methodMap_["hide"] = MethodMetadata {0, __hostFunction_NativeLogBoxSpecJSI_hide};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeModalManagerSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeModalManagerSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativeModalManagerSpecJSI::NativeModalManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeModalManagerSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeModalManagerSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeNetworkingIOS_SpecSendRequestQuery)
++ (RCTManagedPointer *)JS_NativeNetworkingIOS_SpecSendRequestQuery:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeNetworkingIOS::SpecSendRequestQuery>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeNetworkingIOSSpecJSI_sendRequest(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "sendRequest", @selector(sendRequest:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeNetworkingIOSSpecJSI_abortRequest(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "abortRequest", @selector(abortRequest:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeNetworkingIOSSpecJSI_clearCookies(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "clearCookies", @selector(clearCookies:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeNetworkingIOSSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeNetworkingIOSSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativeNetworkingIOSSpecJSI::NativeNetworkingIOSSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["sendRequest"] = MethodMetadata {2, __hostFunction_NativeNetworkingIOSSpecJSI_sendRequest};
+        setMethodArgConversionSelector(@"sendRequest", 0, @"JS_NativeNetworkingIOS_SpecSendRequestQuery:");
+        
+        methodMap_["abortRequest"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSSpecJSI_abortRequest};
+        
+        
+        methodMap_["clearCookies"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSSpecJSI_clearCookies};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativePlatformConstantsIOSSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativePlatformConstantsIOSSpecJSI::NativePlatformConstantsIOSSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativePlatformConstantsIOSSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativePushNotificationManagerIOS_SpecRequestPermissionsPermission)
++ (RCTManagedPointer *)JS_NativePushNotificationManagerIOS_SpecRequestPermissionsPermission:(id)json
+{
+  return facebook::react::managedPointer<JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission>(json);
+}
+@end
+@implementation RCTCxxConvert (NativePushNotificationManagerIOS_Notification)
++ (RCTManagedPointer *)JS_NativePushNotificationManagerIOS_Notification:(id)json
+{
+  return facebook::react::managedPointer<JS::NativePushNotificationManagerIOS::Notification>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_onFinishRemoteNotification(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "onFinishRemoteNotification", @selector(onFinishRemoteNotification:fetchResult:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_setApplicationIconBadgeNumber(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setApplicationIconBadgeNumber", @selector(setApplicationIconBadgeNumber:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_getApplicationIconBadgeNumber(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getApplicationIconBadgeNumber", @selector(getApplicationIconBadgeNumber:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_requestPermissions(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "requestPermissions", @selector(requestPermissions:resolve:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_abandonPermissions(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "abandonPermissions", @selector(abandonPermissions), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_checkPermissions(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "checkPermissions", @selector(checkPermissions:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_presentLocalNotification(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "presentLocalNotification", @selector(presentLocalNotification:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_scheduleLocalNotification(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "scheduleLocalNotification", @selector(scheduleLocalNotification:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_cancelAllLocalNotifications(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "cancelAllLocalNotifications", @selector(cancelAllLocalNotifications), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_cancelLocalNotifications(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "cancelLocalNotifications", @selector(cancelLocalNotifications:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_getInitialNotification(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "getInitialNotification", @selector(getInitialNotification:reject:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_getScheduledLocalNotifications(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getScheduledLocalNotifications", @selector(getScheduledLocalNotifications:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_removeAllDeliveredNotifications(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeAllDeliveredNotifications", @selector(removeAllDeliveredNotifications), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_removeDeliveredNotifications(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeDeliveredNotifications", @selector(removeDeliveredNotifications:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_getDeliveredNotifications(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getDeliveredNotifications", @selector(getDeliveredNotifications:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_getAuthorizationStatus(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getAuthorizationStatus", @selector(getAuthorizationStatus:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativePushNotificationManagerIOSSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativePushNotificationManagerIOSSpecJSI::NativePushNotificationManagerIOSSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["onFinishRemoteNotification"] = MethodMetadata {2, __hostFunction_NativePushNotificationManagerIOSSpecJSI_onFinishRemoteNotification};
+        
+        
+        methodMap_["setApplicationIconBadgeNumber"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_setApplicationIconBadgeNumber};
+        
+        
+        methodMap_["getApplicationIconBadgeNumber"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_getApplicationIconBadgeNumber};
+        
+        
+        methodMap_["requestPermissions"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_requestPermissions};
+        setMethodArgConversionSelector(@"requestPermissions", 0, @"JS_NativePushNotificationManagerIOS_SpecRequestPermissionsPermission:");
+        
+        methodMap_["abandonPermissions"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSSpecJSI_abandonPermissions};
+        
+        
+        methodMap_["checkPermissions"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_checkPermissions};
+        
+        
+        methodMap_["presentLocalNotification"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_presentLocalNotification};
+        setMethodArgConversionSelector(@"presentLocalNotification", 0, @"JS_NativePushNotificationManagerIOS_Notification:");
+        
+        methodMap_["scheduleLocalNotification"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_scheduleLocalNotification};
+        setMethodArgConversionSelector(@"scheduleLocalNotification", 0, @"JS_NativePushNotificationManagerIOS_Notification:");
+        
+        methodMap_["cancelAllLocalNotifications"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSSpecJSI_cancelAllLocalNotifications};
+        
+        
+        methodMap_["cancelLocalNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_cancelLocalNotifications};
+        
+        
+        methodMap_["getInitialNotification"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSSpecJSI_getInitialNotification};
+        
+        
+        methodMap_["getScheduledLocalNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_getScheduledLocalNotifications};
+        
+        
+        methodMap_["removeAllDeliveredNotifications"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSSpecJSI_removeAllDeliveredNotifications};
+        
+        
+        methodMap_["removeDeliveredNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_removeDeliveredNotifications};
+        
+        
+        methodMap_["getDeliveredNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_getDeliveredNotifications};
+        
+        
+        methodMap_["getAuthorizationStatus"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_getAuthorizationStatus};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeRedBoxSpecJSI_setExtraData(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setExtraData", @selector(setExtraData:forIdentifier:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeRedBoxSpecJSI_dismiss(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "dismiss", @selector(dismiss), args, count);
+    }
+
+    NativeRedBoxSpecJSI::NativeRedBoxSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["setExtraData"] = MethodMetadata {2, __hostFunction_NativeRedBoxSpecJSI_setExtraData};
+        
+        
+        methodMap_["dismiss"] = MethodMetadata {0, __hostFunction_NativeRedBoxSpecJSI_dismiss};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeSegmentFetcherSpecJSI_fetchSegment(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "fetchSegment", @selector(fetchSegment:options:callback:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeSegmentFetcherSpecJSI_getSegment(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getSegment", @selector(getSegment:options:callback:), args, count);
+    }
+
+    NativeSegmentFetcherSpecJSI::NativeSegmentFetcherSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["fetchSegment"] = MethodMetadata {3, __hostFunction_NativeSegmentFetcherSpecJSI_fetchSegment};
+        
+        
+        methodMap_["getSegment"] = MethodMetadata {3, __hostFunction_NativeSegmentFetcherSpecJSI_getSegment};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeSettingsManagerSpecJSI_setValues(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setValues", @selector(setValues:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeSettingsManagerSpecJSI_deleteValues(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "deleteValues", @selector(deleteValues:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeSettingsManagerSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativeSettingsManagerSpecJSI::NativeSettingsManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["setValues"] = MethodMetadata {1, __hostFunction_NativeSettingsManagerSpecJSI_setValues};
+        
+        
+        methodMap_["deleteValues"] = MethodMetadata {1, __hostFunction_NativeSettingsManagerSpecJSI_deleteValues};
+        
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeSettingsManagerSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeShareModule_SpecShareContent)
++ (RCTManagedPointer *)JS_NativeShareModule_SpecShareContent:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeShareModule::SpecShareContent>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeShareModuleSpecJSI_share(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, PromiseKind, "share", @selector(share:dialogTitle:resolve:reject:), args, count);
+    }
+
+    NativeShareModuleSpecJSI::NativeShareModuleSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["share"] = MethodMetadata {2, __hostFunction_NativeShareModuleSpecJSI_share};
+        setMethodArgConversionSelector(@"share", 0, @"JS_NativeShareModule_SpecShareContent:");
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeSoundManagerSpecJSI_playTouchSound(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "playTouchSound", @selector(playTouchSound), args, count);
+    }
+
+    NativeSoundManagerSpecJSI::NativeSoundManagerSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["playTouchSound"] = MethodMetadata {0, __hostFunction_NativeSoundManagerSpecJSI_playTouchSound};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeSourceCodeSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativeSourceCodeSpecJSI::NativeSourceCodeSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeSourceCodeSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeStatusBarManagerIOSSpecJSI_getHeight(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "getHeight", @selector(getHeight:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeStatusBarManagerIOSSpecJSI_setNetworkActivityIndicatorVisible(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setNetworkActivityIndicatorVisible", @selector(setNetworkActivityIndicatorVisible:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeStatusBarManagerIOSSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeStatusBarManagerIOSSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeStatusBarManagerIOSSpecJSI_setStyle(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setStyle", @selector(setStyle:animated:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeStatusBarManagerIOSSpecJSI_setHidden(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setHidden", @selector(setHidden:withAnimation:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeStatusBarManagerIOSSpecJSI_getConstants(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, ObjectKind, "getConstants", @selector(getConstants), args, count);
+    }
+
+    NativeStatusBarManagerIOSSpecJSI::NativeStatusBarManagerIOSSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["getHeight"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSSpecJSI_getHeight};
+        
+        
+        methodMap_["setNetworkActivityIndicatorVisible"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSSpecJSI_setNetworkActivityIndicatorVisible};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSSpecJSI_removeListeners};
+        
+        
+        methodMap_["setStyle"] = MethodMetadata {2, __hostFunction_NativeStatusBarManagerIOSSpecJSI_setStyle};
+        
+        
+        methodMap_["setHidden"] = MethodMetadata {2, __hostFunction_NativeStatusBarManagerIOSSpecJSI_setHidden};
+        
+        
+        methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeStatusBarManagerIOSSpecJSI_getConstants};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeTimingSpecJSI_createTimer(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "createTimer", @selector(createTimer:duration:jsSchedulingTime:repeats:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeTimingSpecJSI_deleteTimer(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "deleteTimer", @selector(deleteTimer:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeTimingSpecJSI_setSendIdleEvents(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "setSendIdleEvents", @selector(setSendIdleEvents:), args, count);
+    }
+
+    NativeTimingSpecJSI::NativeTimingSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["createTimer"] = MethodMetadata {4, __hostFunction_NativeTimingSpecJSI_createTimer};
+        
+        
+        methodMap_["deleteTimer"] = MethodMetadata {1, __hostFunction_NativeTimingSpecJSI_deleteTimer};
+        
+        
+        methodMap_["setSendIdleEvents"] = MethodMetadata {1, __hostFunction_NativeTimingSpecJSI_setSendIdleEvents};
+        
+    }
+  } // namespace react
+} // namespace facebook
+
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeVibrationSpecJSI_vibrate(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "vibrate", @selector(vibrate:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeVibrationSpecJSI_vibrateByPattern(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "vibrateByPattern", @selector(vibrateByPattern:repeat:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeVibrationSpecJSI_cancel(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "cancel", @selector(cancel), args, count);
+    }
+
+    NativeVibrationSpecJSI::NativeVibrationSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["vibrate"] = MethodMetadata {1, __hostFunction_NativeVibrationSpecJSI_vibrate};
+        
+        
+        methodMap_["vibrateByPattern"] = MethodMetadata {2, __hostFunction_NativeVibrationSpecJSI_vibrateByPattern};
+        
+        
+        methodMap_["cancel"] = MethodMetadata {0, __hostFunction_NativeVibrationSpecJSI_cancel};
+        
+    }
+  } // namespace react
+} // namespace facebook
+@implementation RCTCxxConvert (NativeWebSocketModule_SpecConnectOptions)
++ (RCTManagedPointer *)JS_NativeWebSocketModule_SpecConnectOptions:(id)json
+{
+  return facebook::react::managedPointer<JS::NativeWebSocketModule::SpecConnectOptions>(json);
+}
+@end
+namespace facebook {
+  namespace react {
+    
+    static facebook::jsi::Value __hostFunction_NativeWebSocketModuleSpecJSI_connect(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "connect", @selector(connect:protocols:options:socketID:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeWebSocketModuleSpecJSI_send(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "send", @selector(send:forSocketID:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeWebSocketModuleSpecJSI_sendBinary(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "sendBinary", @selector(sendBinary:forSocketID:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeWebSocketModuleSpecJSI_ping(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "ping", @selector(ping:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeWebSocketModuleSpecJSI_close(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "close", @selector(close:reason:socketID:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeWebSocketModuleSpecJSI_addListener(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "addListener", @selector(addListener:), args, count);
+    }
+
+    static facebook::jsi::Value __hostFunction_NativeWebSocketModuleSpecJSI_removeListeners(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
+      return static_cast<ObjCTurboModule&>(turboModule).invokeObjCMethod(rt, VoidKind, "removeListeners", @selector(removeListeners:), args, count);
+    }
+
+    NativeWebSocketModuleSpecJSI::NativeWebSocketModuleSpecJSI(const ObjCTurboModule::InitParams &params)
+      : ObjCTurboModule(params) {
+        
+        methodMap_["connect"] = MethodMetadata {4, __hostFunction_NativeWebSocketModuleSpecJSI_connect};
+        setMethodArgConversionSelector(@"connect", 2, @"JS_NativeWebSocketModule_SpecConnectOptions:");
+        
+        methodMap_["send"] = MethodMetadata {2, __hostFunction_NativeWebSocketModuleSpecJSI_send};
+        
+        
+        methodMap_["sendBinary"] = MethodMetadata {2, __hostFunction_NativeWebSocketModuleSpecJSI_sendBinary};
+        
+        
+        methodMap_["ping"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleSpecJSI_ping};
+        
+        
+        methodMap_["close"] = MethodMetadata {3, __hostFunction_NativeWebSocketModuleSpecJSI_close};
+        
+        
+        methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleSpecJSI_addListener};
+        
+        
+        methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleSpecJSI_removeListeners};
+        
+    }
+  } // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/React/FBReactNativeSpec/FBReactNativeSpec/FBReactNativeSpec.h b/node_modules/react-native/React/FBReactNativeSpec/FBReactNativeSpec/FBReactNativeSpec.h
new file mode 100644
index 0000000..0491d34
--- /dev/null
+++ b/node_modules/react-native/React/FBReactNativeSpec/FBReactNativeSpec/FBReactNativeSpec.h
@@ -0,0 +1,2554 @@
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateModuleObjCpp
+ *
+ * We create an umbrella header (and corresponding implementation) here since
+ * Cxx compilation in BUCK has a limitation: source-code producing genrule()s
+ * must have a single output. More files => more genrule()s => slower builds.
+ */
+
+#ifndef __cplusplus
+#error This file must be compiled as Obj-C++. If you are importing it, you must change your file extension to .mm.
+#endif
+#import <Foundation/Foundation.h>
+#import <RCTRequired/RCTRequired.h>
+#import <RCTTypeSafety/RCTConvertHelpers.h>
+#import <RCTTypeSafety/RCTTypedModuleConstants.h>
+#import <React/RCTBridgeModule.h>
+#import <React/RCTCxxConvert.h>
+#import <React/RCTManagedPointer.h>
+#import <ReactCommon/RCTTurboModule.h>
+#import <folly/Optional.h>
+#import <vector>
+
+
+@protocol NativeAccessibilityInfoSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)isReduceMotionEnabled:(RCTResponseSenderBlock)onSuccess;
+- (void)isTouchExplorationEnabled:(RCTResponseSenderBlock)onSuccess;
+- (void)setAccessibilityFocus:(double)reactTag;
+- (void)announceForAccessibility:(NSString *)announcement;
+- (void)getRecommendedTimeoutMillis:(double)mSec
+                          onSuccess:(RCTResponseSenderBlock)onSuccess;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAccessibilityInfo'
+     */
+    class JSI_EXPORT NativeAccessibilityInfoSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAccessibilityInfoSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeAccessibilityManager {
+    struct SpecSetAccessibilityContentSizeMultipliersJSMultipliers {
+      folly::Optional<double> extraSmall() const;
+      folly::Optional<double> small() const;
+      folly::Optional<double> medium() const;
+      folly::Optional<double> large() const;
+      folly::Optional<double> extraLarge() const;
+      folly::Optional<double> extraExtraLarge() const;
+      folly::Optional<double> extraExtraExtraLarge() const;
+      folly::Optional<double> accessibilityMedium() const;
+      folly::Optional<double> accessibilityLarge() const;
+      folly::Optional<double> accessibilityExtraLarge() const;
+      folly::Optional<double> accessibilityExtraExtraLarge() const;
+      folly::Optional<double> accessibilityExtraExtraExtraLarge() const;
+
+      SpecSetAccessibilityContentSizeMultipliersJSMultipliers(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeAccessibilityManager_SpecSetAccessibilityContentSizeMultipliersJSMultipliers)
++ (RCTManagedPointer *)JS_NativeAccessibilityManager_SpecSetAccessibilityContentSizeMultipliersJSMultipliers:(id)json;
+@end
+@protocol NativeAccessibilityManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)getCurrentBoldTextState:(RCTResponseSenderBlock)onSuccess
+                        onError:(RCTResponseSenderBlock)onError;
+- (void)getCurrentGrayscaleState:(RCTResponseSenderBlock)onSuccess
+                         onError:(RCTResponseSenderBlock)onError;
+- (void)getCurrentInvertColorsState:(RCTResponseSenderBlock)onSuccess
+                            onError:(RCTResponseSenderBlock)onError;
+- (void)getCurrentReduceMotionState:(RCTResponseSenderBlock)onSuccess
+                            onError:(RCTResponseSenderBlock)onError;
+- (void)getCurrentReduceTransparencyState:(RCTResponseSenderBlock)onSuccess
+                                  onError:(RCTResponseSenderBlock)onError;
+- (void)getCurrentVoiceOverState:(RCTResponseSenderBlock)onSuccess
+                         onError:(RCTResponseSenderBlock)onError;
+- (void)setAccessibilityContentSizeMultipliers:(JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers &)JSMultipliers;
+- (void)setAccessibilityFocus:(double)reactTag;
+- (void)announceForAccessibility:(NSString *)announcement;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAccessibilityManager'
+     */
+    class JSI_EXPORT NativeAccessibilityManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAccessibilityManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeActionSheetManager {
+    struct SpecShowActionSheetWithOptionsOptions {
+      NSString *title() const;
+      NSString *message() const;
+      folly::Optional<facebook::react::LazyVector<NSString *>> options() const;
+      folly::Optional<facebook::react::LazyVector<double>> destructiveButtonIndices() const;
+      folly::Optional<double> cancelButtonIndex() const;
+      folly::Optional<double> anchor() const;
+      folly::Optional<double> tintColor() const;
+      NSString *userInterfaceStyle() const;
+      folly::Optional<facebook::react::LazyVector<double>> disabledButtonIndices() const;
+
+      SpecShowActionSheetWithOptionsOptions(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeActionSheetManager_SpecShowActionSheetWithOptionsOptions)
++ (RCTManagedPointer *)JS_NativeActionSheetManager_SpecShowActionSheetWithOptionsOptions:(id)json;
+@end
+namespace JS {
+  namespace NativeActionSheetManager {
+    struct SpecShowShareActionSheetWithOptionsOptions {
+      NSString *message() const;
+      NSString *url() const;
+      NSString *subject() const;
+      folly::Optional<double> anchor() const;
+      folly::Optional<double> tintColor() const;
+      folly::Optional<facebook::react::LazyVector<NSString *>> excludedActivityTypes() const;
+      NSString *userInterfaceStyle() const;
+
+      SpecShowShareActionSheetWithOptionsOptions(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeActionSheetManager_SpecShowShareActionSheetWithOptionsOptions)
++ (RCTManagedPointer *)JS_NativeActionSheetManager_SpecShowShareActionSheetWithOptionsOptions:(id)json;
+@end
+@protocol NativeActionSheetManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)showActionSheetWithOptions:(JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions &)options
+                          callback:(RCTResponseSenderBlock)callback;
+- (void)showShareActionSheetWithOptions:(JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions &)options
+                        failureCallback:(RCTResponseSenderBlock)failureCallback
+                        successCallback:(RCTResponseSenderBlock)successCallback;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeActionSheetManager'
+     */
+    class JSI_EXPORT NativeActionSheetManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeActionSheetManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeAlertManager {
+    struct Args {
+      NSString *title() const;
+      NSString *message() const;
+      folly::Optional<facebook::react::LazyVector<id<NSObject> >> buttons() const;
+      NSString *type() const;
+      NSString *defaultValue() const;
+      NSString *cancelButtonKey() const;
+      NSString *destructiveButtonKey() const;
+      NSString *keyboardType() const;
+
+      Args(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeAlertManager_Args)
++ (RCTManagedPointer *)JS_NativeAlertManager_Args:(id)json;
+@end
+@protocol NativeAlertManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)alertWithArgs:(JS::NativeAlertManager::Args &)args
+             callback:(RCTResponseSenderBlock)callback;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAlertManager'
+     */
+    class JSI_EXPORT NativeAlertManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAlertManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeAnimatedModule {
+    struct EventMapping {
+      facebook::react::LazyVector<NSString *> nativeEventPath() const;
+      folly::Optional<double> animatedValueTag() const;
+
+      EventMapping(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeAnimatedModule_EventMapping)
++ (RCTManagedPointer *)JS_NativeAnimatedModule_EventMapping:(id)json;
+@end
+@protocol NativeAnimatedModuleSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)startOperationBatch;
+- (void)finishOperationBatch;
+- (void)createAnimatedNode:(double)tag
+                    config:(NSDictionary *)config;
+- (void)getValue:(double)tag
+saveValueCallback:(RCTResponseSenderBlock)saveValueCallback;
+- (void)startListeningToAnimatedNodeValue:(double)tag;
+- (void)stopListeningToAnimatedNodeValue:(double)tag;
+- (void)connectAnimatedNodes:(double)parentTag
+                    childTag:(double)childTag;
+- (void)disconnectAnimatedNodes:(double)parentTag
+                       childTag:(double)childTag;
+- (void)startAnimatingNode:(double)animationId
+                   nodeTag:(double)nodeTag
+                    config:(NSDictionary *)config
+               endCallback:(RCTResponseSenderBlock)endCallback;
+- (void)stopAnimation:(double)animationId;
+- (void)setAnimatedNodeValue:(double)nodeTag
+                       value:(double)value;
+- (void)setAnimatedNodeOffset:(double)nodeTag
+                       offset:(double)offset;
+- (void)flattenAnimatedNodeOffset:(double)nodeTag;
+- (void)extractAnimatedNodeOffset:(double)nodeTag;
+- (void)connectAnimatedNodeToView:(double)nodeTag
+                          viewTag:(double)viewTag;
+- (void)disconnectAnimatedNodeFromView:(double)nodeTag
+                               viewTag:(double)viewTag;
+- (void)restoreDefaultValues:(double)nodeTag;
+- (void)dropAnimatedNode:(double)tag;
+- (void)addAnimatedEventToView:(double)viewTag
+                     eventName:(NSString *)eventName
+                  eventMapping:(JS::NativeAnimatedModule::EventMapping &)eventMapping;
+- (void)removeAnimatedEventFromView:(double)viewTag
+                          eventName:(NSString *)eventName
+                    animatedNodeTag:(double)animatedNodeTag;
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAnimatedModule'
+     */
+    class JSI_EXPORT NativeAnimatedModuleSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAnimatedModuleSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeAnimatedTurboModule {
+    struct EventMapping {
+      facebook::react::LazyVector<NSString *> nativeEventPath() const;
+      folly::Optional<double> animatedValueTag() const;
+
+      EventMapping(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeAnimatedTurboModule_EventMapping)
++ (RCTManagedPointer *)JS_NativeAnimatedTurboModule_EventMapping:(id)json;
+@end
+@protocol NativeAnimatedTurboModuleSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)startOperationBatch;
+- (void)finishOperationBatch;
+- (void)createAnimatedNode:(double)tag
+                    config:(NSDictionary *)config;
+- (void)getValue:(double)tag
+saveValueCallback:(RCTResponseSenderBlock)saveValueCallback;
+- (void)startListeningToAnimatedNodeValue:(double)tag;
+- (void)stopListeningToAnimatedNodeValue:(double)tag;
+- (void)connectAnimatedNodes:(double)parentTag
+                    childTag:(double)childTag;
+- (void)disconnectAnimatedNodes:(double)parentTag
+                       childTag:(double)childTag;
+- (void)startAnimatingNode:(double)animationId
+                   nodeTag:(double)nodeTag
+                    config:(NSDictionary *)config
+               endCallback:(RCTResponseSenderBlock)endCallback;
+- (void)stopAnimation:(double)animationId;
+- (void)setAnimatedNodeValue:(double)nodeTag
+                       value:(double)value;
+- (void)setAnimatedNodeOffset:(double)nodeTag
+                       offset:(double)offset;
+- (void)flattenAnimatedNodeOffset:(double)nodeTag;
+- (void)extractAnimatedNodeOffset:(double)nodeTag;
+- (void)connectAnimatedNodeToView:(double)nodeTag
+                          viewTag:(double)viewTag;
+- (void)disconnectAnimatedNodeFromView:(double)nodeTag
+                               viewTag:(double)viewTag;
+- (void)restoreDefaultValues:(double)nodeTag;
+- (void)dropAnimatedNode:(double)tag;
+- (void)addAnimatedEventToView:(double)viewTag
+                     eventName:(NSString *)eventName
+                  eventMapping:(JS::NativeAnimatedTurboModule::EventMapping &)eventMapping;
+- (void)removeAnimatedEventFromView:(double)viewTag
+                          eventName:(NSString *)eventName
+                    animatedNodeTag:(double)animatedNodeTag;
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAnimatedTurboModule'
+     */
+    class JSI_EXPORT NativeAnimatedTurboModuleSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAnimatedTurboModuleSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeAnimationsDebugModuleSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)startRecordingFps;
+- (void)stopRecordingFps:(double)animationStopTimeMs;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAnimationsDebugModule'
+     */
+    class JSI_EXPORT NativeAnimationsDebugModuleSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAnimationsDebugModuleSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeAppState {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<NSString *> initialAppState;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativeAppStateSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)getCurrentAppState:(RCTResponseSenderBlock)success
+                     error:(RCTResponseSenderBlock)error;
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+- (facebook::react::ModuleConstants<JS::NativeAppState::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativeAppState::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAppState'
+     */
+    class JSI_EXPORT NativeAppStateSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAppStateSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeAppearanceSpec <RCTBridgeModule, RCTTurboModule>
+
+- (NSString * _Nullable)getColorScheme;
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAppearance'
+     */
+    class JSI_EXPORT NativeAppearanceSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAppearanceSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeAsyncLocalStorageSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)multiGet:(NSArray *)keys
+        callback:(RCTResponseSenderBlock)callback;
+- (void)multiSet:(NSArray *)kvPairs
+        callback:(RCTResponseSenderBlock)callback;
+- (void)multiMerge:(NSArray *)kvPairs
+          callback:(RCTResponseSenderBlock)callback;
+- (void)multiRemove:(NSArray *)keys
+           callback:(RCTResponseSenderBlock)callback;
+- (void)clear:(RCTResponseSenderBlock)callback;
+- (void)getAllKeys:(RCTResponseSenderBlock)callback;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAsyncLocalStorage'
+     */
+    class JSI_EXPORT NativeAsyncLocalStorageSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAsyncLocalStorageSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeAsyncSQLiteDBStorageSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)multiGet:(NSArray *)keys
+        callback:(RCTResponseSenderBlock)callback;
+- (void)multiSet:(NSArray *)kvPairs
+        callback:(RCTResponseSenderBlock)callback;
+- (void)multiMerge:(NSArray *)kvPairs
+          callback:(RCTResponseSenderBlock)callback;
+- (void)multiRemove:(NSArray *)keys
+           callback:(RCTResponseSenderBlock)callback;
+- (void)clear:(RCTResponseSenderBlock)callback;
+- (void)getAllKeys:(RCTResponseSenderBlock)callback;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeAsyncSQLiteDBStorage'
+     */
+    class JSI_EXPORT NativeAsyncSQLiteDBStorageSpecJSI : public ObjCTurboModule {
+    public:
+      NativeAsyncSQLiteDBStorageSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeBlobModule {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<NSString *> BLOB_URI_SCHEME;
+          RCTRequired<NSString *> BLOB_URI_HOST;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativeBlobModuleSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)addNetworkingHandler;
+- (void)addWebSocketHandler:(double)id;
+- (void)removeWebSocketHandler:(double)id;
+- (void)sendOverSocket:(NSDictionary *)blob
+              socketID:(double)socketID;
+- (void)createFromParts:(NSArray *)parts
+                 withId:(NSString *)withId;
+- (void)release:(NSString *)blobId;
+- (facebook::react::ModuleConstants<JS::NativeBlobModule::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativeBlobModule::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeBlobModule'
+     */
+    class JSI_EXPORT NativeBlobModuleSpecJSI : public ObjCTurboModule {
+    public:
+      NativeBlobModuleSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeBugReportingSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)startReportAProblemFlow;
+- (void)setExtraData:(NSDictionary *)extraData
+          extraFiles:(NSDictionary *)extraFiles;
+- (void)setCategoryID:(NSString *)categoryID;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeBugReporting'
+     */
+    class JSI_EXPORT NativeBugReportingSpecJSI : public ObjCTurboModule {
+    public:
+      NativeBugReportingSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeClipboardSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)getString:(RCTPromiseResolveBlock)resolve
+           reject:(RCTPromiseRejectBlock)reject;
+- (void)setString:(NSString *)content;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeClipboard'
+     */
+    class JSI_EXPORT NativeClipboardSpecJSI : public ObjCTurboModule {
+    public:
+      NativeClipboardSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeDevLoadingViewSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)showMessage:(NSString *)message
+          withColor:(NSNumber *)withColor
+withBackgroundColor:(NSNumber *)withBackgroundColor;
+- (void)hide;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeDevLoadingView'
+     */
+    class JSI_EXPORT NativeDevLoadingViewSpecJSI : public ObjCTurboModule {
+    public:
+      NativeDevLoadingViewSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeDevMenuSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)show;
+- (void)reload;
+- (void)debugRemotely:(BOOL)enableDebug;
+- (void)setProfilingEnabled:(BOOL)enabled;
+- (void)setHotLoadingEnabled:(BOOL)enabled;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeDevMenu'
+     */
+    class JSI_EXPORT NativeDevMenuSpecJSI : public ObjCTurboModule {
+    public:
+      NativeDevMenuSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeDevSettingsSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)reload;
+- (void)reloadWithReason:(NSString *)reason;
+- (void)onFastRefresh;
+- (void)setHotLoadingEnabled:(BOOL)isHotLoadingEnabled;
+- (void)setIsDebuggingRemotely:(BOOL)isDebuggingRemotelyEnabled;
+- (void)setProfilingEnabled:(BOOL)isProfilingEnabled;
+- (void)toggleElementInspector;
+- (void)addMenuItem:(NSString *)title;
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+- (void)setIsShakeToShowDevMenuEnabled:(BOOL)enabled;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeDevSettings'
+     */
+    class JSI_EXPORT NativeDevSettingsSpecJSI : public ObjCTurboModule {
+    public:
+      NativeDevSettingsSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeDevSplitBundleLoaderSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)loadBundle:(NSString *)bundlePath
+           resolve:(RCTPromiseResolveBlock)resolve
+            reject:(RCTPromiseRejectBlock)reject;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeDevSplitBundleLoader'
+     */
+    class JSI_EXPORT NativeDevSplitBundleLoaderSpecJSI : public ObjCTurboModule {
+    public:
+      NativeDevSplitBundleLoaderSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeDeviceEventManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)invokeDefaultBackPressHandler;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeDeviceEventManager'
+     */
+    class JSI_EXPORT NativeDeviceEventManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeDeviceEventManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeDeviceInfo {
+    struct DisplayMetrics {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<double> width;
+          RCTRequired<double> height;
+          RCTRequired<double> scale;
+          RCTRequired<double> fontScale;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing DisplayMetrics */
+        Builder(DisplayMetrics i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static DisplayMetrics fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      DisplayMetrics(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+namespace JS {
+  namespace NativeDeviceInfo {
+    struct DisplayMetricsAndroid {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<double> width;
+          RCTRequired<double> height;
+          RCTRequired<double> scale;
+          RCTRequired<double> fontScale;
+          RCTRequired<double> densityDpi;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing DisplayMetricsAndroid */
+        Builder(DisplayMetricsAndroid i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static DisplayMetricsAndroid fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      DisplayMetricsAndroid(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+namespace JS {
+  namespace NativeDeviceInfo {
+    struct DimensionsPayload {
+
+      struct Builder {
+        struct Input {
+          folly::Optional<JS::NativeDeviceInfo::DisplayMetrics::Builder> window;
+          folly::Optional<JS::NativeDeviceInfo::DisplayMetrics::Builder> screen;
+          folly::Optional<JS::NativeDeviceInfo::DisplayMetricsAndroid::Builder> windowPhysicalPixels;
+          folly::Optional<JS::NativeDeviceInfo::DisplayMetricsAndroid::Builder> screenPhysicalPixels;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing DimensionsPayload */
+        Builder(DimensionsPayload i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static DimensionsPayload fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      DimensionsPayload(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+namespace JS {
+  namespace NativeDeviceInfo {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<JS::NativeDeviceInfo::DimensionsPayload::Builder> Dimensions;
+          folly::Optional<bool> isIPhoneX_deprecated;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativeDeviceInfoSpec <RCTBridgeModule, RCTTurboModule>
+
+- (facebook::react::ModuleConstants<JS::NativeDeviceInfo::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativeDeviceInfo::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeDeviceInfo'
+     */
+    class JSI_EXPORT NativeDeviceInfoSpecJSI : public ObjCTurboModule {
+    public:
+      NativeDeviceInfoSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeExceptionsManager {
+    struct StackFrame {
+      folly::Optional<double> column() const;
+      NSString *file() const;
+      folly::Optional<double> lineNumber() const;
+      NSString *methodName() const;
+      folly::Optional<bool> collapse() const;
+
+      StackFrame(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeExceptionsManager_StackFrame)
++ (RCTManagedPointer *)JS_NativeExceptionsManager_StackFrame:(id)json;
+@end
+namespace JS {
+  namespace NativeExceptionsManager {
+    struct ExceptionData {
+      NSString *message() const;
+      NSString *originalMessage() const;
+      NSString *name() const;
+      NSString *componentStack() const;
+      facebook::react::LazyVector<JS::NativeExceptionsManager::StackFrame> stack() const;
+      double id_() const;
+      bool isFatal() const;
+      id<NSObject> _Nullable extraData() const;
+
+      ExceptionData(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeExceptionsManager_ExceptionData)
++ (RCTManagedPointer *)JS_NativeExceptionsManager_ExceptionData:(id)json;
+@end
+@protocol NativeExceptionsManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)reportFatalException:(NSString *)message
+                       stack:(NSArray *)stack
+                 exceptionId:(double)exceptionId;
+- (void)reportSoftException:(NSString *)message
+                      stack:(NSArray *)stack
+                exceptionId:(double)exceptionId;
+- (void)reportException:(JS::NativeExceptionsManager::ExceptionData &)data;
+- (void)updateExceptionMessage:(NSString *)message
+                         stack:(NSArray *)stack
+                   exceptionId:(double)exceptionId;
+- (void)dismissRedbox;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeExceptionsManager'
+     */
+    class JSI_EXPORT NativeExceptionsManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeExceptionsManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeFileReaderModuleSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)readAsDataURL:(NSDictionary *)data
+              resolve:(RCTPromiseResolveBlock)resolve
+               reject:(RCTPromiseRejectBlock)reject;
+- (void)readAsText:(NSDictionary *)data
+          encoding:(NSString *)encoding
+           resolve:(RCTPromiseResolveBlock)resolve
+            reject:(RCTPromiseRejectBlock)reject;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeFileReaderModule'
+     */
+    class JSI_EXPORT NativeFileReaderModuleSpecJSI : public ObjCTurboModule {
+    public:
+      NativeFileReaderModuleSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeFrameRateLogger {
+    struct SpecSetGlobalOptionsOptions {
+      folly::Optional<bool> debug() const;
+      folly::Optional<bool> reportStackTraces() const;
+
+      SpecSetGlobalOptionsOptions(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeFrameRateLogger_SpecSetGlobalOptionsOptions)
++ (RCTManagedPointer *)JS_NativeFrameRateLogger_SpecSetGlobalOptionsOptions:(id)json;
+@end
+@protocol NativeFrameRateLoggerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)setGlobalOptions:(JS::NativeFrameRateLogger::SpecSetGlobalOptionsOptions &)options;
+- (void)setContext:(NSString *)context;
+- (void)beginScroll;
+- (void)endScroll;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeFrameRateLogger'
+     */
+    class JSI_EXPORT NativeFrameRateLoggerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeFrameRateLoggerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeHeadlessJsTaskSupportSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)notifyTaskFinished:(double)taskId;
+- (void)notifyTaskRetry:(double)taskId
+                resolve:(RCTPromiseResolveBlock)resolve
+                 reject:(RCTPromiseRejectBlock)reject;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeHeadlessJsTaskSupport'
+     */
+    class JSI_EXPORT NativeHeadlessJsTaskSupportSpecJSI : public ObjCTurboModule {
+    public:
+      NativeHeadlessJsTaskSupportSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeI18nManager {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<bool> isRTL;
+          RCTRequired<bool> doLeftAndRightSwapInRTL;
+          RCTRequired<NSString *> localeIdentifier;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativeI18nManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)allowRTL:(BOOL)allowRTL;
+- (void)forceRTL:(BOOL)forceRTL;
+- (void)swapLeftAndRightInRTL:(BOOL)flipStyles;
+- (facebook::react::ModuleConstants<JS::NativeI18nManager::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativeI18nManager::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeI18nManager'
+     */
+    class JSI_EXPORT NativeI18nManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeI18nManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeImageEditor {
+    struct OptionsOffset {
+      double x() const;
+      double y() const;
+
+      OptionsOffset(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeImageEditor_OptionsOffset)
++ (RCTManagedPointer *)JS_NativeImageEditor_OptionsOffset:(id)json;
+@end
+namespace JS {
+  namespace NativeImageEditor {
+    struct OptionsSize {
+      double width() const;
+      double height() const;
+
+      OptionsSize(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeImageEditor_OptionsSize)
++ (RCTManagedPointer *)JS_NativeImageEditor_OptionsSize:(id)json;
+@end
+namespace JS {
+  namespace NativeImageEditor {
+    struct OptionsDisplaySize {
+      double width() const;
+      double height() const;
+
+      OptionsDisplaySize(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeImageEditor_OptionsDisplaySize)
++ (RCTManagedPointer *)JS_NativeImageEditor_OptionsDisplaySize:(id)json;
+@end
+namespace JS {
+  namespace NativeImageEditor {
+    struct Options {
+      JS::NativeImageEditor::OptionsOffset offset() const;
+      JS::NativeImageEditor::OptionsSize size() const;
+      folly::Optional<JS::NativeImageEditor::OptionsDisplaySize> displaySize() const;
+      NSString *resizeMode() const;
+      folly::Optional<bool> allowExternalStorage() const;
+
+      Options(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeImageEditor_Options)
++ (RCTManagedPointer *)JS_NativeImageEditor_Options:(id)json;
+@end
+@protocol NativeImageEditorSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)cropImage:(NSString *)uri
+         cropData:(JS::NativeImageEditor::Options &)cropData
+  successCallback:(RCTResponseSenderBlock)successCallback
+    errorCallback:(RCTResponseSenderBlock)errorCallback;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeImageEditor'
+     */
+    class JSI_EXPORT NativeImageEditorSpecJSI : public ObjCTurboModule {
+    public:
+      NativeImageEditorSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeImageLoaderIOSSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)getSize:(NSString *)uri
+        resolve:(RCTPromiseResolveBlock)resolve
+         reject:(RCTPromiseRejectBlock)reject;
+- (void)getSizeWithHeaders:(NSString *)uri
+                   headers:(NSDictionary *)headers
+                   resolve:(RCTPromiseResolveBlock)resolve
+                    reject:(RCTPromiseRejectBlock)reject;
+- (void)prefetchImage:(NSString *)uri
+              resolve:(RCTPromiseResolveBlock)resolve
+               reject:(RCTPromiseRejectBlock)reject;
+- (void)prefetchImageWithMetadata:(NSString *)uri
+                    queryRootName:(NSString *)queryRootName
+                          rootTag:(double)rootTag
+                          resolve:(RCTPromiseResolveBlock)resolve
+                           reject:(RCTPromiseRejectBlock)reject;
+- (void)queryCache:(NSArray *)uris
+           resolve:(RCTPromiseResolveBlock)resolve
+            reject:(RCTPromiseRejectBlock)reject;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeImageLoaderIOS'
+     */
+    class JSI_EXPORT NativeImageLoaderIOSSpecJSI : public ObjCTurboModule {
+    public:
+      NativeImageLoaderIOSSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeImagePickerIOS {
+    struct SpecOpenCameraDialogConfig {
+      bool unmirrorFrontFacingCamera() const;
+      bool videoMode() const;
+
+      SpecOpenCameraDialogConfig(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeImagePickerIOS_SpecOpenCameraDialogConfig)
++ (RCTManagedPointer *)JS_NativeImagePickerIOS_SpecOpenCameraDialogConfig:(id)json;
+@end
+namespace JS {
+  namespace NativeImagePickerIOS {
+    struct SpecOpenSelectDialogConfig {
+      bool showImages() const;
+      bool showVideos() const;
+
+      SpecOpenSelectDialogConfig(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeImagePickerIOS_SpecOpenSelectDialogConfig)
++ (RCTManagedPointer *)JS_NativeImagePickerIOS_SpecOpenSelectDialogConfig:(id)json;
+@end
+@protocol NativeImagePickerIOSSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)canRecordVideos:(RCTResponseSenderBlock)callback;
+- (void)canUseCamera:(RCTResponseSenderBlock)callback;
+- (void)openCameraDialog:(JS::NativeImagePickerIOS::SpecOpenCameraDialogConfig &)config
+         successCallback:(RCTResponseSenderBlock)successCallback
+          cancelCallback:(RCTResponseSenderBlock)cancelCallback;
+- (void)openSelectDialog:(JS::NativeImagePickerIOS::SpecOpenSelectDialogConfig &)config
+         successCallback:(RCTResponseSenderBlock)successCallback
+          cancelCallback:(RCTResponseSenderBlock)cancelCallback;
+- (void)clearAllPendingVideos;
+- (void)removePendingVideo:(NSString *)url;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeImagePickerIOS'
+     */
+    class JSI_EXPORT NativeImagePickerIOSSpecJSI : public ObjCTurboModule {
+    public:
+      NativeImagePickerIOSSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeImageStoreIOSSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)getBase64ForTag:(NSString *)uri
+        successCallback:(RCTResponseSenderBlock)successCallback
+          errorCallback:(RCTResponseSenderBlock)errorCallback;
+- (void)hasImageForTag:(NSString *)uri
+              callback:(RCTResponseSenderBlock)callback;
+- (void)removeImageForTag:(NSString *)uri;
+- (void)addImageFromBase64:(NSString *)base64ImageData
+           successCallback:(RCTResponseSenderBlock)successCallback
+             errorCallback:(RCTResponseSenderBlock)errorCallback;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeImageStoreIOS'
+     */
+    class JSI_EXPORT NativeImageStoreIOSSpecJSI : public ObjCTurboModule {
+    public:
+      NativeImageStoreIOSSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeJSCHeapCaptureSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)captureComplete:(NSString *)path
+                  error:(NSString * _Nullable)error;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeJSCHeapCapture'
+     */
+    class JSI_EXPORT NativeJSCHeapCaptureSpecJSI : public ObjCTurboModule {
+    public:
+      NativeJSCHeapCaptureSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeJSCSamplingProfilerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)operationComplete:(double)token
+                   result:(NSString * _Nullable)result
+                    error:(NSString * _Nullable)error;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeJSCSamplingProfiler'
+     */
+    class JSI_EXPORT NativeJSCSamplingProfilerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeJSCSamplingProfilerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeJSDevSupport {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<double> ERROR_CODE_EXCEPTION;
+          RCTRequired<double> ERROR_CODE_VIEW_NOT_FOUND;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativeJSDevSupportSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)onSuccess:(NSString *)data;
+- (void)onFailure:(double)errorCode
+            error:(NSString *)error;
+- (facebook::react::ModuleConstants<JS::NativeJSDevSupport::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativeJSDevSupport::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeJSDevSupport'
+     */
+    class JSI_EXPORT NativeJSDevSupportSpecJSI : public ObjCTurboModule {
+    public:
+      NativeJSDevSupportSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeKeyboardObserverSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeKeyboardObserver'
+     */
+    class JSI_EXPORT NativeKeyboardObserverSpecJSI : public ObjCTurboModule {
+    public:
+      NativeKeyboardObserverSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeLinkingManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)getInitialURL:(RCTPromiseResolveBlock)resolve
+               reject:(RCTPromiseRejectBlock)reject;
+- (void)canOpenURL:(NSString *)url
+           resolve:(RCTPromiseResolveBlock)resolve
+            reject:(RCTPromiseRejectBlock)reject;
+- (void)openURL:(NSString *)url
+        resolve:(RCTPromiseResolveBlock)resolve
+         reject:(RCTPromiseRejectBlock)reject;
+- (void)openSettings:(RCTPromiseResolveBlock)resolve
+              reject:(RCTPromiseRejectBlock)reject;
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeLinkingManager'
+     */
+    class JSI_EXPORT NativeLinkingManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeLinkingManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeLogBoxSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)show;
+- (void)hide;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeLogBox'
+     */
+    class JSI_EXPORT NativeLogBoxSpecJSI : public ObjCTurboModule {
+    public:
+      NativeLogBoxSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeModalManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeModalManager'
+     */
+    class JSI_EXPORT NativeModalManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeModalManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeNetworkingIOS {
+    struct SpecSendRequestQuery {
+      NSString *method() const;
+      NSString *url() const;
+      id<NSObject>  data() const;
+      id<NSObject>  headers() const;
+      NSString *responseType() const;
+      bool incrementalUpdates() const;
+      double timeout() const;
+      bool withCredentials() const;
+
+      SpecSendRequestQuery(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeNetworkingIOS_SpecSendRequestQuery)
++ (RCTManagedPointer *)JS_NativeNetworkingIOS_SpecSendRequestQuery:(id)json;
+@end
+@protocol NativeNetworkingIOSSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)sendRequest:(JS::NativeNetworkingIOS::SpecSendRequestQuery &)query
+           callback:(RCTResponseSenderBlock)callback;
+- (void)abortRequest:(double)requestId;
+- (void)clearCookies:(RCTResponseSenderBlock)callback;
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeNetworkingIOS'
+     */
+    class JSI_EXPORT NativeNetworkingIOSSpecJSI : public ObjCTurboModule {
+    public:
+      NativeNetworkingIOSSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativePlatformConstantsIOS {
+    struct ConstantsReactNativeVersion {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<double> major;
+          RCTRequired<double> minor;
+          RCTRequired<double> patch;
+          RCTRequired<folly::Optional<double>> prerelease;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing ConstantsReactNativeVersion */
+        Builder(ConstantsReactNativeVersion i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static ConstantsReactNativeVersion fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      ConstantsReactNativeVersion(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+namespace JS {
+  namespace NativePlatformConstantsIOS {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<bool> isTesting;
+          RCTRequired<JS::NativePlatformConstantsIOS::ConstantsReactNativeVersion::Builder> reactNativeVersion;
+          RCTRequired<bool> forceTouchAvailable;
+          RCTRequired<NSString *> osVersion;
+          RCTRequired<NSString *> systemName;
+          RCTRequired<NSString *> interfaceIdiom;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativePlatformConstantsIOSSpec <RCTBridgeModule, RCTTurboModule>
+
+- (facebook::react::ModuleConstants<JS::NativePlatformConstantsIOS::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativePlatformConstantsIOS::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativePlatformConstantsIOS'
+     */
+    class JSI_EXPORT NativePlatformConstantsIOSSpecJSI : public ObjCTurboModule {
+    public:
+      NativePlatformConstantsIOSSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativePushNotificationManagerIOS {
+    struct SpecRequestPermissionsPermission {
+      bool alert() const;
+      bool badge() const;
+      bool sound() const;
+
+      SpecRequestPermissionsPermission(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativePushNotificationManagerIOS_SpecRequestPermissionsPermission)
++ (RCTManagedPointer *)JS_NativePushNotificationManagerIOS_SpecRequestPermissionsPermission:(id)json;
+@end
+namespace JS {
+  namespace NativePushNotificationManagerIOS {
+    struct Notification {
+      NSString *alertTitle() const;
+      folly::Optional<double> fireDate() const;
+      NSString *alertBody() const;
+      NSString *alertAction() const;
+      id<NSObject> _Nullable userInfo() const;
+      NSString *category() const;
+      NSString *repeatInterval() const;
+      folly::Optional<double> applicationIconBadgeNumber() const;
+      folly::Optional<bool> isSilent() const;
+
+      Notification(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativePushNotificationManagerIOS_Notification)
++ (RCTManagedPointer *)JS_NativePushNotificationManagerIOS_Notification:(id)json;
+@end
+@protocol NativePushNotificationManagerIOSSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)onFinishRemoteNotification:(NSString *)notificationId
+                       fetchResult:(NSString *)fetchResult;
+- (void)setApplicationIconBadgeNumber:(double)num;
+- (void)getApplicationIconBadgeNumber:(RCTResponseSenderBlock)callback;
+- (void)requestPermissions:(JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission &)permission
+                   resolve:(RCTPromiseResolveBlock)resolve
+                    reject:(RCTPromiseRejectBlock)reject;
+- (void)abandonPermissions;
+- (void)checkPermissions:(RCTResponseSenderBlock)callback;
+- (void)presentLocalNotification:(JS::NativePushNotificationManagerIOS::Notification &)notification;
+- (void)scheduleLocalNotification:(JS::NativePushNotificationManagerIOS::Notification &)notification;
+- (void)cancelAllLocalNotifications;
+- (void)cancelLocalNotifications:(NSDictionary *)userInfo;
+- (void)getInitialNotification:(RCTPromiseResolveBlock)resolve
+                        reject:(RCTPromiseRejectBlock)reject;
+- (void)getScheduledLocalNotifications:(RCTResponseSenderBlock)callback;
+- (void)removeAllDeliveredNotifications;
+- (void)removeDeliveredNotifications:(NSArray *)identifiers;
+- (void)getDeliveredNotifications:(RCTResponseSenderBlock)callback;
+- (void)getAuthorizationStatus:(RCTResponseSenderBlock)callback;
+- (void)addListener:(NSString *)eventType;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativePushNotificationManagerIOS'
+     */
+    class JSI_EXPORT NativePushNotificationManagerIOSSpecJSI : public ObjCTurboModule {
+    public:
+      NativePushNotificationManagerIOSSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeRedBoxSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)setExtraData:(NSDictionary *)extraData
+       forIdentifier:(NSString *)forIdentifier;
+- (void)dismiss;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeRedBox'
+     */
+    class JSI_EXPORT NativeRedBoxSpecJSI : public ObjCTurboModule {
+    public:
+      NativeRedBoxSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeSegmentFetcherSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)fetchSegment:(double)segmentId
+             options:(NSDictionary *)options
+            callback:(RCTResponseSenderBlock)callback;
+- (void)getSegment:(double)segmentId
+           options:(NSDictionary *)options
+          callback:(RCTResponseSenderBlock)callback;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeSegmentFetcher'
+     */
+    class JSI_EXPORT NativeSegmentFetcherSpecJSI : public ObjCTurboModule {
+    public:
+      NativeSegmentFetcherSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeSettingsManager {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<id<NSObject> > settings;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativeSettingsManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)setValues:(NSDictionary *)values;
+- (void)deleteValues:(NSArray *)values;
+- (facebook::react::ModuleConstants<JS::NativeSettingsManager::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativeSettingsManager::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeSettingsManager'
+     */
+    class JSI_EXPORT NativeSettingsManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeSettingsManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeShareModule {
+    struct SpecShareContent {
+      NSString *title() const;
+      NSString *message() const;
+
+      SpecShareContent(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeShareModule_SpecShareContent)
++ (RCTManagedPointer *)JS_NativeShareModule_SpecShareContent:(id)json;
+@end
+@protocol NativeShareModuleSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)share:(JS::NativeShareModule::SpecShareContent &)content
+  dialogTitle:(NSString *)dialogTitle
+      resolve:(RCTPromiseResolveBlock)resolve
+       reject:(RCTPromiseRejectBlock)reject;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeShareModule'
+     */
+    class JSI_EXPORT NativeShareModuleSpecJSI : public ObjCTurboModule {
+    public:
+      NativeShareModuleSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeSoundManagerSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)playTouchSound;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeSoundManager'
+     */
+    class JSI_EXPORT NativeSoundManagerSpecJSI : public ObjCTurboModule {
+    public:
+      NativeSoundManagerSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeSourceCode {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<NSString *> scriptURL;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativeSourceCodeSpec <RCTBridgeModule, RCTTurboModule>
+
+- (facebook::react::ModuleConstants<JS::NativeSourceCode::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativeSourceCode::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeSourceCode'
+     */
+    class JSI_EXPORT NativeSourceCodeSpecJSI : public ObjCTurboModule {
+    public:
+      NativeSourceCodeSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeStatusBarManagerIOS {
+    struct Constants {
+
+      struct Builder {
+        struct Input {
+          RCTRequired<double> HEIGHT;
+          folly::Optional<double> DEFAULT_BACKGROUND_COLOR;
+        };
+
+        /** Initialize with a set of values */
+        Builder(const Input i);
+        /** Initialize with an existing Constants */
+        Builder(Constants i);
+        /** Builds the object. Generally used only by the infrastructure. */
+        NSDictionary *buildUnsafeRawValue() const { return _factory(); };
+      private:
+        NSDictionary *(^_factory)(void);
+      };
+
+      static Constants fromUnsafeRawValue(NSDictionary *const v) { return {v}; }
+      NSDictionary *unsafeRawValue() const { return _v; }
+    private:
+      Constants(NSDictionary *const v) : _v(v) {}
+      NSDictionary *_v;
+    };
+  }
+}
+@protocol NativeStatusBarManagerIOSSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)getHeight:(RCTResponseSenderBlock)callback;
+- (void)setNetworkActivityIndicatorVisible:(BOOL)visible;
+- (void)addListener:(NSString *)eventType;
+- (void)removeListeners:(double)count;
+- (void)setStyle:(NSString * _Nullable)statusBarStyle
+        animated:(BOOL)animated;
+- (void)setHidden:(BOOL)hidden
+    withAnimation:(NSString *)withAnimation;
+- (facebook::react::ModuleConstants<JS::NativeStatusBarManagerIOS::Constants::Builder>)constantsToExport;
+- (facebook::react::ModuleConstants<JS::NativeStatusBarManagerIOS::Constants::Builder>)getConstants;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeStatusBarManagerIOS'
+     */
+    class JSI_EXPORT NativeStatusBarManagerIOSSpecJSI : public ObjCTurboModule {
+    public:
+      NativeStatusBarManagerIOSSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeTimingSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)createTimer:(double)callbackID
+           duration:(double)duration
+   jsSchedulingTime:(double)jsSchedulingTime
+            repeats:(BOOL)repeats;
+- (void)deleteTimer:(double)timerID;
+- (void)setSendIdleEvents:(BOOL)sendIdleEvents;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeTiming'
+     */
+    class JSI_EXPORT NativeTimingSpecJSI : public ObjCTurboModule {
+    public:
+      NativeTimingSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+@protocol NativeVibrationSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)vibrate:(double)pattern;
+- (void)vibrateByPattern:(NSArray *)pattern
+                  repeat:(double)repeat;
+- (void)cancel;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeVibration'
+     */
+    class JSI_EXPORT NativeVibrationSpecJSI : public ObjCTurboModule {
+    public:
+      NativeVibrationSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+namespace JS {
+  namespace NativeWebSocketModule {
+    struct SpecConnectOptions {
+      id<NSObject> _Nullable headers() const;
+
+      SpecConnectOptions(NSDictionary *const v) : _v(v) {}
+    private:
+      NSDictionary *_v;
+    };
+  }
+}
+
+@interface RCTCxxConvert (NativeWebSocketModule_SpecConnectOptions)
++ (RCTManagedPointer *)JS_NativeWebSocketModule_SpecConnectOptions:(id)json;
+@end
+@protocol NativeWebSocketModuleSpec <RCTBridgeModule, RCTTurboModule>
+
+- (void)connect:(NSString *)url
+      protocols:(NSArray * _Nullable)protocols
+        options:(JS::NativeWebSocketModule::SpecConnectOptions &)options
+       socketID:(double)socketID;
+- (void)send:(NSString *)message
+ forSocketID:(double)forSocketID;
+- (void)sendBinary:(NSString *)base64String
+       forSocketID:(double)forSocketID;
+- (void)ping:(double)socketID;
+- (void)close:(double)code
+       reason:(NSString *)reason
+     socketID:(double)socketID;
+- (void)addListener:(NSString *)eventName;
+- (void)removeListeners:(double)count;
+
+@end
+namespace facebook {
+  namespace react {
+    /**
+     * ObjC++ class for module 'NativeWebSocketModule'
+     */
+    class JSI_EXPORT NativeWebSocketModuleSpecJSI : public ObjCTurboModule {
+    public:
+      NativeWebSocketModuleSpecJSI(const ObjCTurboModule::InitParams &params);
+    };
+  } // namespace react
+} // namespace facebook
+
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::extraSmall() const
+{
+  id const p = _v[@"extraSmall"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::small() const
+{
+  id const p = _v[@"small"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::medium() const
+{
+  id const p = _v[@"medium"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::large() const
+{
+  id const p = _v[@"large"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::extraLarge() const
+{
+  id const p = _v[@"extraLarge"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::extraExtraLarge() const
+{
+  id const p = _v[@"extraExtraLarge"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::extraExtraExtraLarge() const
+{
+  id const p = _v[@"extraExtraExtraLarge"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityMedium() const
+{
+  id const p = _v[@"accessibilityMedium"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityLarge() const
+{
+  id const p = _v[@"accessibilityLarge"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityExtraLarge() const
+{
+  id const p = _v[@"accessibilityExtraLarge"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityExtraExtraLarge() const
+{
+  id const p = _v[@"accessibilityExtraExtraLarge"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeAccessibilityManager::SpecSetAccessibilityContentSizeMultipliersJSMultipliers::accessibilityExtraExtraExtraLarge() const
+{
+  id const p = _v[@"accessibilityExtraExtraExtraLarge"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline NSString *JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::title() const
+{
+  id const p = _v[@"title"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::message() const
+{
+  id const p = _v[@"message"];
+  return RCTBridgingToOptionalString(p);
+}
+inline folly::Optional<facebook::react::LazyVector<NSString *>> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::options() const
+{
+  id const p = _v[@"options"];
+  return RCTBridgingToOptionalVec(p, ^NSString *(id itemValue_0) { return RCTBridgingToString(itemValue_0); });
+}
+inline folly::Optional<facebook::react::LazyVector<double>> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::destructiveButtonIndices() const
+{
+  id const p = _v[@"destructiveButtonIndices"];
+  return RCTBridgingToOptionalVec(p, ^double(id itemValue_0) { return RCTBridgingToDouble(itemValue_0); });
+}
+inline folly::Optional<double> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::cancelButtonIndex() const
+{
+  id const p = _v[@"cancelButtonIndex"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::anchor() const
+{
+  id const p = _v[@"anchor"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::tintColor() const
+{
+  id const p = _v[@"tintColor"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline NSString *JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::userInterfaceStyle() const
+{
+  id const p = _v[@"userInterfaceStyle"];
+  return RCTBridgingToOptionalString(p);
+}
+inline folly::Optional<facebook::react::LazyVector<double>> JS::NativeActionSheetManager::SpecShowActionSheetWithOptionsOptions::disabledButtonIndices() const
+{
+  id const p = _v[@"disabledButtonIndices"];
+  return RCTBridgingToOptionalVec(p, ^double(id itemValue_0) { return RCTBridgingToDouble(itemValue_0); });
+}
+inline NSString *JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::message() const
+{
+  id const p = _v[@"message"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::url() const
+{
+  id const p = _v[@"url"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::subject() const
+{
+  id const p = _v[@"subject"];
+  return RCTBridgingToOptionalString(p);
+}
+inline folly::Optional<double> JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::anchor() const
+{
+  id const p = _v[@"anchor"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<double> JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::tintColor() const
+{
+  id const p = _v[@"tintColor"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<facebook::react::LazyVector<NSString *>> JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::excludedActivityTypes() const
+{
+  id const p = _v[@"excludedActivityTypes"];
+  return RCTBridgingToOptionalVec(p, ^NSString *(id itemValue_0) { return RCTBridgingToString(itemValue_0); });
+}
+inline NSString *JS::NativeActionSheetManager::SpecShowShareActionSheetWithOptionsOptions::userInterfaceStyle() const
+{
+  id const p = _v[@"userInterfaceStyle"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeAlertManager::Args::title() const
+{
+  id const p = _v[@"title"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeAlertManager::Args::message() const
+{
+  id const p = _v[@"message"];
+  return RCTBridgingToOptionalString(p);
+}
+inline folly::Optional<facebook::react::LazyVector<id<NSObject> >> JS::NativeAlertManager::Args::buttons() const
+{
+  id const p = _v[@"buttons"];
+  return RCTBridgingToOptionalVec(p, ^id<NSObject> (id itemValue_0) { return itemValue_0; });
+}
+inline NSString *JS::NativeAlertManager::Args::type() const
+{
+  id const p = _v[@"type"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeAlertManager::Args::defaultValue() const
+{
+  id const p = _v[@"defaultValue"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeAlertManager::Args::cancelButtonKey() const
+{
+  id const p = _v[@"cancelButtonKey"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeAlertManager::Args::destructiveButtonKey() const
+{
+  id const p = _v[@"destructiveButtonKey"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeAlertManager::Args::keyboardType() const
+{
+  id const p = _v[@"keyboardType"];
+  return RCTBridgingToOptionalString(p);
+}
+inline facebook::react::LazyVector<NSString *> JS::NativeAnimatedModule::EventMapping::nativeEventPath() const
+{
+  id const p = _v[@"nativeEventPath"];
+  return RCTBridgingToVec(p, ^NSString *(id itemValue_0) { return RCTBridgingToString(itemValue_0); });
+}
+inline folly::Optional<double> JS::NativeAnimatedModule::EventMapping::animatedValueTag() const
+{
+  id const p = _v[@"animatedValueTag"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline facebook::react::LazyVector<NSString *> JS::NativeAnimatedTurboModule::EventMapping::nativeEventPath() const
+{
+  id const p = _v[@"nativeEventPath"];
+  return RCTBridgingToVec(p, ^NSString *(id itemValue_0) { return RCTBridgingToString(itemValue_0); });
+}
+inline folly::Optional<double> JS::NativeAnimatedTurboModule::EventMapping::animatedValueTag() const
+{
+  id const p = _v[@"animatedValueTag"];
+  return RCTBridgingToOptionalDouble(p);
+}
+
+inline JS::NativeAppState::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto initialAppState = i.initialAppState.get();
+  d[@"initialAppState"] = initialAppState;
+  return d;
+}) {}
+inline JS::NativeAppState::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+
+
+
+inline JS::NativeBlobModule::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto BLOB_URI_SCHEME = i.BLOB_URI_SCHEME.get();
+  d[@"BLOB_URI_SCHEME"] = BLOB_URI_SCHEME;
+  auto BLOB_URI_HOST = i.BLOB_URI_HOST.get();
+  d[@"BLOB_URI_HOST"] = BLOB_URI_HOST;
+  return d;
+}) {}
+inline JS::NativeBlobModule::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+
+
+
+
+
+
+
+inline JS::NativeDeviceInfo::DisplayMetrics::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto width = i.width.get();
+  d[@"width"] = @(width);
+  auto height = i.height.get();
+  d[@"height"] = @(height);
+  auto scale = i.scale.get();
+  d[@"scale"] = @(scale);
+  auto fontScale = i.fontScale.get();
+  d[@"fontScale"] = @(fontScale);
+  return d;
+}) {}
+inline JS::NativeDeviceInfo::DisplayMetrics::Builder::Builder(DisplayMetrics i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline JS::NativeDeviceInfo::DisplayMetricsAndroid::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto width = i.width.get();
+  d[@"width"] = @(width);
+  auto height = i.height.get();
+  d[@"height"] = @(height);
+  auto scale = i.scale.get();
+  d[@"scale"] = @(scale);
+  auto fontScale = i.fontScale.get();
+  d[@"fontScale"] = @(fontScale);
+  auto densityDpi = i.densityDpi.get();
+  d[@"densityDpi"] = @(densityDpi);
+  return d;
+}) {}
+inline JS::NativeDeviceInfo::DisplayMetricsAndroid::Builder::Builder(DisplayMetricsAndroid i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline JS::NativeDeviceInfo::DimensionsPayload::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto window = i.window;
+  d[@"window"] = window.hasValue() ? window.value().buildUnsafeRawValue() : nil;
+  auto screen = i.screen;
+  d[@"screen"] = screen.hasValue() ? screen.value().buildUnsafeRawValue() : nil;
+  auto windowPhysicalPixels = i.windowPhysicalPixels;
+  d[@"windowPhysicalPixels"] = windowPhysicalPixels.hasValue() ? windowPhysicalPixels.value().buildUnsafeRawValue() : nil;
+  auto screenPhysicalPixels = i.screenPhysicalPixels;
+  d[@"screenPhysicalPixels"] = screenPhysicalPixels.hasValue() ? screenPhysicalPixels.value().buildUnsafeRawValue() : nil;
+  return d;
+}) {}
+inline JS::NativeDeviceInfo::DimensionsPayload::Builder::Builder(DimensionsPayload i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline JS::NativeDeviceInfo::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto Dimensions = i.Dimensions.get();
+  d[@"Dimensions"] = Dimensions.buildUnsafeRawValue();
+  auto isIPhoneX_deprecated = i.isIPhoneX_deprecated;
+  d[@"isIPhoneX_deprecated"] = isIPhoneX_deprecated.hasValue() ? @((BOOL)isIPhoneX_deprecated.value()) : nil;
+  return d;
+}) {}
+inline JS::NativeDeviceInfo::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline folly::Optional<double> JS::NativeExceptionsManager::StackFrame::column() const
+{
+  id const p = _v[@"column"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline NSString *JS::NativeExceptionsManager::StackFrame::file() const
+{
+  id const p = _v[@"file"];
+  return RCTBridgingToOptionalString(p);
+}
+inline folly::Optional<double> JS::NativeExceptionsManager::StackFrame::lineNumber() const
+{
+  id const p = _v[@"lineNumber"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline NSString *JS::NativeExceptionsManager::StackFrame::methodName() const
+{
+  id const p = _v[@"methodName"];
+  return RCTBridgingToString(p);
+}
+inline folly::Optional<bool> JS::NativeExceptionsManager::StackFrame::collapse() const
+{
+  id const p = _v[@"collapse"];
+  return RCTBridgingToOptionalBool(p);
+}
+inline NSString *JS::NativeExceptionsManager::ExceptionData::message() const
+{
+  id const p = _v[@"message"];
+  return RCTBridgingToString(p);
+}
+inline NSString *JS::NativeExceptionsManager::ExceptionData::originalMessage() const
+{
+  id const p = _v[@"originalMessage"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeExceptionsManager::ExceptionData::name() const
+{
+  id const p = _v[@"name"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeExceptionsManager::ExceptionData::componentStack() const
+{
+  id const p = _v[@"componentStack"];
+  return RCTBridgingToOptionalString(p);
+}
+inline facebook::react::LazyVector<JS::NativeExceptionsManager::StackFrame> JS::NativeExceptionsManager::ExceptionData::stack() const
+{
+  id const p = _v[@"stack"];
+  return RCTBridgingToVec(p, ^JS::NativeExceptionsManager::StackFrame(id itemValue_0) { return JS::NativeExceptionsManager::StackFrame(itemValue_0); });
+}
+inline double JS::NativeExceptionsManager::ExceptionData::id_() const
+{
+  id const p = _v[@"id"];
+  return RCTBridgingToDouble(p);
+}
+inline bool JS::NativeExceptionsManager::ExceptionData::isFatal() const
+{
+  id const p = _v[@"isFatal"];
+  return RCTBridgingToBool(p);
+}
+inline id<NSObject> _Nullable JS::NativeExceptionsManager::ExceptionData::extraData() const
+{
+  id const p = _v[@"extraData"];
+  return p;
+}
+
+inline folly::Optional<bool> JS::NativeFrameRateLogger::SpecSetGlobalOptionsOptions::debug() const
+{
+  id const p = _v[@"debug"];
+  return RCTBridgingToOptionalBool(p);
+}
+inline folly::Optional<bool> JS::NativeFrameRateLogger::SpecSetGlobalOptionsOptions::reportStackTraces() const
+{
+  id const p = _v[@"reportStackTraces"];
+  return RCTBridgingToOptionalBool(p);
+}
+
+inline JS::NativeI18nManager::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto isRTL = i.isRTL.get();
+  d[@"isRTL"] = @(isRTL);
+  auto doLeftAndRightSwapInRTL = i.doLeftAndRightSwapInRTL.get();
+  d[@"doLeftAndRightSwapInRTL"] = @(doLeftAndRightSwapInRTL);
+  auto localeIdentifier = i.localeIdentifier.get();
+  d[@"localeIdentifier"] = localeIdentifier;
+  return d;
+}) {}
+inline JS::NativeI18nManager::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline double JS::NativeImageEditor::OptionsOffset::x() const
+{
+  id const p = _v[@"x"];
+  return RCTBridgingToDouble(p);
+}
+inline double JS::NativeImageEditor::OptionsOffset::y() const
+{
+  id const p = _v[@"y"];
+  return RCTBridgingToDouble(p);
+}
+inline double JS::NativeImageEditor::OptionsSize::width() const
+{
+  id const p = _v[@"width"];
+  return RCTBridgingToDouble(p);
+}
+inline double JS::NativeImageEditor::OptionsSize::height() const
+{
+  id const p = _v[@"height"];
+  return RCTBridgingToDouble(p);
+}
+inline double JS::NativeImageEditor::OptionsDisplaySize::width() const
+{
+  id const p = _v[@"width"];
+  return RCTBridgingToDouble(p);
+}
+inline double JS::NativeImageEditor::OptionsDisplaySize::height() const
+{
+  id const p = _v[@"height"];
+  return RCTBridgingToDouble(p);
+}
+inline JS::NativeImageEditor::OptionsOffset JS::NativeImageEditor::Options::offset() const
+{
+  id const p = _v[@"offset"];
+  return JS::NativeImageEditor::OptionsOffset(p);
+}
+inline JS::NativeImageEditor::OptionsSize JS::NativeImageEditor::Options::size() const
+{
+  id const p = _v[@"size"];
+  return JS::NativeImageEditor::OptionsSize(p);
+}
+inline folly::Optional<JS::NativeImageEditor::OptionsDisplaySize> JS::NativeImageEditor::Options::displaySize() const
+{
+  id const p = _v[@"displaySize"];
+  return (p == nil ? folly::none : folly::make_optional(JS::NativeImageEditor::OptionsDisplaySize(p)));
+}
+inline NSString *JS::NativeImageEditor::Options::resizeMode() const
+{
+  id const p = _v[@"resizeMode"];
+  return RCTBridgingToOptionalString(p);
+}
+inline folly::Optional<bool> JS::NativeImageEditor::Options::allowExternalStorage() const
+{
+  id const p = _v[@"allowExternalStorage"];
+  return RCTBridgingToOptionalBool(p);
+}
+
+inline bool JS::NativeImagePickerIOS::SpecOpenCameraDialogConfig::unmirrorFrontFacingCamera() const
+{
+  id const p = _v[@"unmirrorFrontFacingCamera"];
+  return RCTBridgingToBool(p);
+}
+inline bool JS::NativeImagePickerIOS::SpecOpenCameraDialogConfig::videoMode() const
+{
+  id const p = _v[@"videoMode"];
+  return RCTBridgingToBool(p);
+}
+inline bool JS::NativeImagePickerIOS::SpecOpenSelectDialogConfig::showImages() const
+{
+  id const p = _v[@"showImages"];
+  return RCTBridgingToBool(p);
+}
+inline bool JS::NativeImagePickerIOS::SpecOpenSelectDialogConfig::showVideos() const
+{
+  id const p = _v[@"showVideos"];
+  return RCTBridgingToBool(p);
+}
+
+
+
+inline JS::NativeJSDevSupport::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto ERROR_CODE_EXCEPTION = i.ERROR_CODE_EXCEPTION.get();
+  d[@"ERROR_CODE_EXCEPTION"] = @(ERROR_CODE_EXCEPTION);
+  auto ERROR_CODE_VIEW_NOT_FOUND = i.ERROR_CODE_VIEW_NOT_FOUND.get();
+  d[@"ERROR_CODE_VIEW_NOT_FOUND"] = @(ERROR_CODE_VIEW_NOT_FOUND);
+  return d;
+}) {}
+inline JS::NativeJSDevSupport::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+
+
+
+
+inline NSString *JS::NativeNetworkingIOS::SpecSendRequestQuery::method() const
+{
+  id const p = _v[@"method"];
+  return RCTBridgingToString(p);
+}
+inline NSString *JS::NativeNetworkingIOS::SpecSendRequestQuery::url() const
+{
+  id const p = _v[@"url"];
+  return RCTBridgingToString(p);
+}
+inline id<NSObject>  JS::NativeNetworkingIOS::SpecSendRequestQuery::data() const
+{
+  id const p = _v[@"data"];
+  return p;
+}
+inline id<NSObject>  JS::NativeNetworkingIOS::SpecSendRequestQuery::headers() const
+{
+  id const p = _v[@"headers"];
+  return p;
+}
+inline NSString *JS::NativeNetworkingIOS::SpecSendRequestQuery::responseType() const
+{
+  id const p = _v[@"responseType"];
+  return RCTBridgingToString(p);
+}
+inline bool JS::NativeNetworkingIOS::SpecSendRequestQuery::incrementalUpdates() const
+{
+  id const p = _v[@"incrementalUpdates"];
+  return RCTBridgingToBool(p);
+}
+inline double JS::NativeNetworkingIOS::SpecSendRequestQuery::timeout() const
+{
+  id const p = _v[@"timeout"];
+  return RCTBridgingToDouble(p);
+}
+inline bool JS::NativeNetworkingIOS::SpecSendRequestQuery::withCredentials() const
+{
+  id const p = _v[@"withCredentials"];
+  return RCTBridgingToBool(p);
+}
+inline JS::NativePlatformConstantsIOS::ConstantsReactNativeVersion::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto major = i.major.get();
+  d[@"major"] = @(major);
+  auto minor = i.minor.get();
+  d[@"minor"] = @(minor);
+  auto patch = i.patch.get();
+  d[@"patch"] = @(patch);
+  auto prerelease = i.prerelease.get();
+  d[@"prerelease"] = prerelease.hasValue() ? @((double)prerelease.value()) : nil;
+  return d;
+}) {}
+inline JS::NativePlatformConstantsIOS::ConstantsReactNativeVersion::Builder::Builder(ConstantsReactNativeVersion i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline JS::NativePlatformConstantsIOS::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto isTesting = i.isTesting.get();
+  d[@"isTesting"] = @(isTesting);
+  auto reactNativeVersion = i.reactNativeVersion.get();
+  d[@"reactNativeVersion"] = reactNativeVersion.buildUnsafeRawValue();
+  auto forceTouchAvailable = i.forceTouchAvailable.get();
+  d[@"forceTouchAvailable"] = @(forceTouchAvailable);
+  auto osVersion = i.osVersion.get();
+  d[@"osVersion"] = osVersion;
+  auto systemName = i.systemName.get();
+  d[@"systemName"] = systemName;
+  auto interfaceIdiom = i.interfaceIdiom.get();
+  d[@"interfaceIdiom"] = interfaceIdiom;
+  return d;
+}) {}
+inline JS::NativePlatformConstantsIOS::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline bool JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission::alert() const
+{
+  id const p = _v[@"alert"];
+  return RCTBridgingToBool(p);
+}
+inline bool JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission::badge() const
+{
+  id const p = _v[@"badge"];
+  return RCTBridgingToBool(p);
+}
+inline bool JS::NativePushNotificationManagerIOS::SpecRequestPermissionsPermission::sound() const
+{
+  id const p = _v[@"sound"];
+  return RCTBridgingToBool(p);
+}
+inline NSString *JS::NativePushNotificationManagerIOS::Notification::alertTitle() const
+{
+  id const p = _v[@"alertTitle"];
+  return RCTBridgingToOptionalString(p);
+}
+inline folly::Optional<double> JS::NativePushNotificationManagerIOS::Notification::fireDate() const
+{
+  id const p = _v[@"fireDate"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline NSString *JS::NativePushNotificationManagerIOS::Notification::alertBody() const
+{
+  id const p = _v[@"alertBody"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativePushNotificationManagerIOS::Notification::alertAction() const
+{
+  id const p = _v[@"alertAction"];
+  return RCTBridgingToOptionalString(p);
+}
+inline id<NSObject> _Nullable JS::NativePushNotificationManagerIOS::Notification::userInfo() const
+{
+  id const p = _v[@"userInfo"];
+  return p;
+}
+inline NSString *JS::NativePushNotificationManagerIOS::Notification::category() const
+{
+  id const p = _v[@"category"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativePushNotificationManagerIOS::Notification::repeatInterval() const
+{
+  id const p = _v[@"repeatInterval"];
+  return RCTBridgingToOptionalString(p);
+}
+inline folly::Optional<double> JS::NativePushNotificationManagerIOS::Notification::applicationIconBadgeNumber() const
+{
+  id const p = _v[@"applicationIconBadgeNumber"];
+  return RCTBridgingToOptionalDouble(p);
+}
+inline folly::Optional<bool> JS::NativePushNotificationManagerIOS::Notification::isSilent() const
+{
+  id const p = _v[@"isSilent"];
+  return RCTBridgingToOptionalBool(p);
+}
+
+
+inline JS::NativeSettingsManager::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto settings = i.settings.get();
+  d[@"settings"] = settings;
+  return d;
+}) {}
+inline JS::NativeSettingsManager::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline NSString *JS::NativeShareModule::SpecShareContent::title() const
+{
+  id const p = _v[@"title"];
+  return RCTBridgingToOptionalString(p);
+}
+inline NSString *JS::NativeShareModule::SpecShareContent::message() const
+{
+  id const p = _v[@"message"];
+  return RCTBridgingToOptionalString(p);
+}
+
+inline JS::NativeSourceCode::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto scriptURL = i.scriptURL.get();
+  d[@"scriptURL"] = scriptURL;
+  return d;
+}) {}
+inline JS::NativeSourceCode::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+inline JS::NativeStatusBarManagerIOS::Constants::Builder::Builder(const Input i) : _factory(^{
+  NSMutableDictionary *d = [NSMutableDictionary new];
+  auto HEIGHT = i.HEIGHT.get();
+  d[@"HEIGHT"] = @(HEIGHT);
+  auto DEFAULT_BACKGROUND_COLOR = i.DEFAULT_BACKGROUND_COLOR;
+  d[@"DEFAULT_BACKGROUND_COLOR"] = DEFAULT_BACKGROUND_COLOR.hasValue() ? @((double)DEFAULT_BACKGROUND_COLOR.value()) : nil;
+  return d;
+}) {}
+inline JS::NativeStatusBarManagerIOS::Constants::Builder::Builder(Constants i) : _factory(^{
+  return i.unsafeRawValue();
+}) {}
+
+
+inline id<NSObject> _Nullable JS::NativeWebSocketModule::SpecConnectOptions::headers() const
+{
+  id const p = _v[@"headers"];
+  return p;
+}
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h
new file mode 100644
index 0000000..aa2cc67
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h
@@ -0,0 +1,32 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateComponentDescriptorH.js
+ */
+
+#pragma once
+
+#include <react/renderer/components/rncore/ShadowNodes.h>
+#include <react/renderer/core/ConcreteComponentDescriptor.h>
+
+namespace facebook {
+namespace react {
+
+using ActivityIndicatorViewComponentDescriptor = ConcreteComponentDescriptor<ActivityIndicatorViewShadowNode>;
+using DatePickerComponentDescriptor = ConcreteComponentDescriptor<DatePickerShadowNode>;
+using AndroidDrawerLayoutComponentDescriptor = ConcreteComponentDescriptor<AndroidDrawerLayoutShadowNode>;
+using RCTMaskedViewComponentDescriptor = ConcreteComponentDescriptor<RCTMaskedViewShadowNode>;
+using RCTProgressViewComponentDescriptor = ConcreteComponentDescriptor<RCTProgressViewShadowNode>;
+using AndroidSwipeRefreshLayoutComponentDescriptor = ConcreteComponentDescriptor<AndroidSwipeRefreshLayoutShadowNode>;
+using PullToRefreshViewComponentDescriptor = ConcreteComponentDescriptor<PullToRefreshViewShadowNode>;
+using AndroidHorizontalScrollContentViewComponentDescriptor = ConcreteComponentDescriptor<AndroidHorizontalScrollContentViewShadowNode>;
+using RCTSegmentedControlComponentDescriptor = ConcreteComponentDescriptor<RCTSegmentedControlShadowNode>;
+using SwitchComponentDescriptor = ConcreteComponentDescriptor<SwitchShadowNode>;
+using UnimplementedNativeViewComponentDescriptor = ConcreteComponentDescriptor<UnimplementedNativeViewShadowNode>;
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h.bak b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h.bak
new file mode 100644
index 0000000..670e3c7
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ComponentDescriptors.h.bak
@@ -0,0 +1,32 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateComponentDescriptorH.js
+ */
+
+#pragma once
+
+#include <react/renderer/components/FBReactNativeSpec/ShadowNodes.h>
+#include <react/renderer/core/ConcreteComponentDescriptor.h>
+
+namespace facebook {
+namespace react {
+
+using ActivityIndicatorViewComponentDescriptor = ConcreteComponentDescriptor<ActivityIndicatorViewShadowNode>;
+using DatePickerComponentDescriptor = ConcreteComponentDescriptor<DatePickerShadowNode>;
+using AndroidDrawerLayoutComponentDescriptor = ConcreteComponentDescriptor<AndroidDrawerLayoutShadowNode>;
+using RCTMaskedViewComponentDescriptor = ConcreteComponentDescriptor<RCTMaskedViewShadowNode>;
+using RCTProgressViewComponentDescriptor = ConcreteComponentDescriptor<RCTProgressViewShadowNode>;
+using AndroidSwipeRefreshLayoutComponentDescriptor = ConcreteComponentDescriptor<AndroidSwipeRefreshLayoutShadowNode>;
+using PullToRefreshViewComponentDescriptor = ConcreteComponentDescriptor<PullToRefreshViewShadowNode>;
+using AndroidHorizontalScrollContentViewComponentDescriptor = ConcreteComponentDescriptor<AndroidHorizontalScrollContentViewShadowNode>;
+using RCTSegmentedControlComponentDescriptor = ConcreteComponentDescriptor<RCTSegmentedControlShadowNode>;
+using SwitchComponentDescriptor = ConcreteComponentDescriptor<SwitchShadowNode>;
+using UnimplementedNativeViewComponentDescriptor = ConcreteComponentDescriptor<UnimplementedNativeViewShadowNode>;
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp
new file mode 100644
index 0000000..b5ea483
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp
@@ -0,0 +1,149 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateEventEmitterCpp.js
+ */
+
+#include <react/renderer/components/rncore/EventEmitters.h>
+
+namespace facebook {
+namespace react {
+
+
+void DatePickerEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "timestamp", event.timestamp);
+    return payload;
+  });
+}
+void AndroidDrawerLayoutEventEmitter::onDrawerSlide(OnDrawerSlide event) const {
+  dispatchEvent("drawerSlide", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "offset", event.offset);
+    return payload;
+  });
+}
+void AndroidDrawerLayoutEventEmitter::onDrawerStateChanged(OnDrawerStateChanged event) const {
+  dispatchEvent("drawerStateChanged", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "drawerState", event.drawerState);
+    return payload;
+  });
+}
+void AndroidDrawerLayoutEventEmitter::onDrawerOpen(OnDrawerOpen event) const {
+  dispatchEvent("drawerOpen", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void AndroidDrawerLayoutEventEmitter::onDrawerClose(OnDrawerClose event) const {
+  dispatchEvent("drawerClose", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+
+
+
+void AndroidSwipeRefreshLayoutEventEmitter::onRefresh(OnRefresh event) const {
+  dispatchEvent("refresh", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void PullToRefreshViewEventEmitter::onRefresh(OnRefresh event) const {
+  dispatchEvent("refresh", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+
+
+void RCTSegmentedControlEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+payload.setProperty(runtime, "selectedSegmentIndex", event.selectedSegmentIndex);
+    return payload;
+  });
+}
+void SliderEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+payload.setProperty(runtime, "fromUser", event.fromUser);
+    return payload;
+  });
+}
+void SliderEventEmitter::onValueChange(OnValueChange event) const {
+  dispatchEvent("valueChange", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+payload.setProperty(runtime, "fromUser", event.fromUser);
+    return payload;
+  });
+}
+void SliderEventEmitter::onSlidingComplete(OnSlidingComplete event) const {
+  dispatchEvent("slidingComplete", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+payload.setProperty(runtime, "fromUser", event.fromUser);
+    return payload;
+  });
+}
+void AndroidSwitchEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+    return payload;
+  });
+}
+void SwitchEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+    return payload;
+  });
+}
+
+
+void ModalHostViewEventEmitter::onRequestClose(OnRequestClose event) const {
+  dispatchEvent("requestClose", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void ModalHostViewEventEmitter::onShow(OnShow event) const {
+  dispatchEvent("show", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void ModalHostViewEventEmitter::onDismiss(OnDismiss event) const {
+  dispatchEvent("dismiss", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void ModalHostViewEventEmitter::onOrientationChange(OnOrientationChange event) const {
+  dispatchEvent("orientationChange", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "orientation", toString(event.orientation));
+    return payload;
+  });
+}
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp.bak b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp.bak
new file mode 100644
index 0000000..ee7153b
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.cpp.bak
@@ -0,0 +1,149 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateEventEmitterCpp.js
+ */
+
+#include <react/renderer/components/FBReactNativeSpec/EventEmitters.h>
+
+namespace facebook {
+namespace react {
+
+
+void DatePickerEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "timestamp", event.timestamp);
+    return payload;
+  });
+}
+void AndroidDrawerLayoutEventEmitter::onDrawerSlide(OnDrawerSlide event) const {
+  dispatchEvent("drawerSlide", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "offset", event.offset);
+    return payload;
+  });
+}
+void AndroidDrawerLayoutEventEmitter::onDrawerStateChanged(OnDrawerStateChanged event) const {
+  dispatchEvent("drawerStateChanged", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "drawerState", event.drawerState);
+    return payload;
+  });
+}
+void AndroidDrawerLayoutEventEmitter::onDrawerOpen(OnDrawerOpen event) const {
+  dispatchEvent("drawerOpen", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void AndroidDrawerLayoutEventEmitter::onDrawerClose(OnDrawerClose event) const {
+  dispatchEvent("drawerClose", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+
+
+
+void AndroidSwipeRefreshLayoutEventEmitter::onRefresh(OnRefresh event) const {
+  dispatchEvent("refresh", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void PullToRefreshViewEventEmitter::onRefresh(OnRefresh event) const {
+  dispatchEvent("refresh", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+
+
+void RCTSegmentedControlEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+payload.setProperty(runtime, "selectedSegmentIndex", event.selectedSegmentIndex);
+    return payload;
+  });
+}
+void SliderEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+payload.setProperty(runtime, "fromUser", event.fromUser);
+    return payload;
+  });
+}
+void SliderEventEmitter::onValueChange(OnValueChange event) const {
+  dispatchEvent("valueChange", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+payload.setProperty(runtime, "fromUser", event.fromUser);
+    return payload;
+  });
+}
+void SliderEventEmitter::onSlidingComplete(OnSlidingComplete event) const {
+  dispatchEvent("slidingComplete", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+payload.setProperty(runtime, "fromUser", event.fromUser);
+    return payload;
+  });
+}
+void AndroidSwitchEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+    return payload;
+  });
+}
+void SwitchEventEmitter::onChange(OnChange event) const {
+  dispatchEvent("change", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "value", event.value);
+    return payload;
+  });
+}
+
+
+void ModalHostViewEventEmitter::onRequestClose(OnRequestClose event) const {
+  dispatchEvent("requestClose", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void ModalHostViewEventEmitter::onShow(OnShow event) const {
+  dispatchEvent("show", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void ModalHostViewEventEmitter::onDismiss(OnDismiss event) const {
+  dispatchEvent("dismiss", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    
+    return payload;
+  });
+}
+void ModalHostViewEventEmitter::onOrientationChange(OnOrientationChange event) const {
+  dispatchEvent("orientationChange", [event=std::move(event)](jsi::Runtime &runtime) {
+    auto payload = jsi::Object(runtime);
+    payload.setProperty(runtime, "orientation", toString(event.orientation));
+    return payload;
+  });
+}
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h
new file mode 100644
index 0000000..e925cf9
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h
@@ -0,0 +1,237 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateEventEmitterH.js
+ */
+#pragma once
+
+#include <react/renderer/components/view/ViewEventEmitter.h>
+
+namespace facebook {
+namespace react {
+
+class ActivityIndicatorViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class DatePickerEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      Float timestamp;
+    };
+
+  void onChange(OnChange value) const;
+};
+class AndroidDrawerLayoutEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnDrawerSlide {
+      Float offset;
+    };
+
+  struct OnDrawerStateChanged {
+      int drawerState;
+    };
+
+  struct OnDrawerOpen {
+      
+    };
+
+  struct OnDrawerClose {
+      
+    };
+
+  void onDrawerSlide(OnDrawerSlide value) const;
+
+  void onDrawerStateChanged(OnDrawerStateChanged value) const;
+
+  void onDrawerOpen(OnDrawerOpen value) const;
+
+  void onDrawerClose(OnDrawerClose value) const;
+};
+class RCTMaskedViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class AndroidProgressBarEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class RCTProgressViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class AndroidSwipeRefreshLayoutEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRefresh {
+      
+    };
+
+  void onRefresh(OnRefresh value) const;
+};
+class PullToRefreshViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRefresh {
+      
+    };
+
+  void onRefresh(OnRefresh value) const;
+};
+class SafeAreaViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class AndroidHorizontalScrollContentViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class RCTSegmentedControlEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      int value;
+    int selectedSegmentIndex;
+    };
+
+  void onChange(OnChange value) const;
+};
+class SliderEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      double value;
+    bool fromUser;
+    };
+
+  struct OnValueChange {
+      double value;
+    bool fromUser;
+    };
+
+  struct OnSlidingComplete {
+      double value;
+    bool fromUser;
+    };
+
+  void onChange(OnChange value) const;
+
+  void onValueChange(OnValueChange value) const;
+
+  void onSlidingComplete(OnSlidingComplete value) const;
+};
+class AndroidSwitchEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      bool value;
+    };
+
+  void onChange(OnChange value) const;
+};
+class SwitchEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      bool value;
+    };
+
+  void onChange(OnChange value) const;
+};
+class InputAccessoryEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class UnimplementedNativeViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class ModalHostViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRequestClose {
+      
+    };
+
+  struct OnShow {
+      
+    };
+
+  struct OnDismiss {
+      
+    };
+
+  enum class OnOrientationChangeOrientation {
+    Portrait,
+    Landscape
+  };
+
+  static char const *toString(const OnOrientationChangeOrientation value) {
+    switch (value) {
+      case OnOrientationChangeOrientation::Portrait: return "portrait";
+      case OnOrientationChangeOrientation::Landscape: return "landscape";
+    }
+  }
+
+  struct OnOrientationChange {
+      OnOrientationChangeOrientation orientation;
+    };
+
+  void onRequestClose(OnRequestClose value) const;
+
+  void onShow(OnShow value) const;
+
+  void onDismiss(OnDismiss value) const;
+
+  void onOrientationChange(OnOrientationChange value) const;
+};
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h.bak b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h.bak
new file mode 100644
index 0000000..e925cf9
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/EventEmitters.h.bak
@@ -0,0 +1,237 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateEventEmitterH.js
+ */
+#pragma once
+
+#include <react/renderer/components/view/ViewEventEmitter.h>
+
+namespace facebook {
+namespace react {
+
+class ActivityIndicatorViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class DatePickerEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      Float timestamp;
+    };
+
+  void onChange(OnChange value) const;
+};
+class AndroidDrawerLayoutEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnDrawerSlide {
+      Float offset;
+    };
+
+  struct OnDrawerStateChanged {
+      int drawerState;
+    };
+
+  struct OnDrawerOpen {
+      
+    };
+
+  struct OnDrawerClose {
+      
+    };
+
+  void onDrawerSlide(OnDrawerSlide value) const;
+
+  void onDrawerStateChanged(OnDrawerStateChanged value) const;
+
+  void onDrawerOpen(OnDrawerOpen value) const;
+
+  void onDrawerClose(OnDrawerClose value) const;
+};
+class RCTMaskedViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class AndroidProgressBarEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class RCTProgressViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class AndroidSwipeRefreshLayoutEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRefresh {
+      
+    };
+
+  void onRefresh(OnRefresh value) const;
+};
+class PullToRefreshViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRefresh {
+      
+    };
+
+  void onRefresh(OnRefresh value) const;
+};
+class SafeAreaViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class AndroidHorizontalScrollContentViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class RCTSegmentedControlEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      int value;
+    int selectedSegmentIndex;
+    };
+
+  void onChange(OnChange value) const;
+};
+class SliderEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      double value;
+    bool fromUser;
+    };
+
+  struct OnValueChange {
+      double value;
+    bool fromUser;
+    };
+
+  struct OnSlidingComplete {
+      double value;
+    bool fromUser;
+    };
+
+  void onChange(OnChange value) const;
+
+  void onValueChange(OnValueChange value) const;
+
+  void onSlidingComplete(OnSlidingComplete value) const;
+};
+class AndroidSwitchEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      bool value;
+    };
+
+  void onChange(OnChange value) const;
+};
+class SwitchEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnChange {
+      bool value;
+    };
+
+  void onChange(OnChange value) const;
+};
+class InputAccessoryEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class UnimplementedNativeViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  
+
+  
+};
+class ModalHostViewEventEmitter : public ViewEventEmitter {
+ public:
+  using ViewEventEmitter::ViewEventEmitter;
+
+  struct OnRequestClose {
+      
+    };
+
+  struct OnShow {
+      
+    };
+
+  struct OnDismiss {
+      
+    };
+
+  enum class OnOrientationChangeOrientation {
+    Portrait,
+    Landscape
+  };
+
+  static char const *toString(const OnOrientationChangeOrientation value) {
+    switch (value) {
+      case OnOrientationChangeOrientation::Portrait: return "portrait";
+      case OnOrientationChangeOrientation::Landscape: return "landscape";
+    }
+  }
+
+  struct OnOrientationChange {
+      OnOrientationChangeOrientation orientation;
+    };
+
+  void onRequestClose(OnRequestClose value) const;
+
+  void onShow(OnShow value) const;
+
+  void onDismiss(OnDismiss value) const;
+
+  void onOrientationChange(OnOrientationChange value) const;
+};
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp
new file mode 100644
index 0000000..808d4ce
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp
@@ -0,0 +1,200 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GeneratePropsCpp.js
+ */
+
+#include <react/renderer/components/rncore/Props.h>
+#include <react/renderer/components/image/conversions.h>
+#include <react/renderer/core/propsConversions.h>
+
+namespace facebook {
+namespace react {
+
+ActivityIndicatorViewProps::ActivityIndicatorViewProps(
+    const ActivityIndicatorViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    hidesWhenStopped(convertRawProp(rawProps, "hidesWhenStopped", sourceProps.hidesWhenStopped, {false})),
+    animating(convertRawProp(rawProps, "animating", sourceProps.animating, {false})),
+    color(convertRawProp(rawProps, "color", sourceProps.color, {})),
+    size(convertRawProp(rawProps, "size", sourceProps.size, {ActivityIndicatorViewSize::Small}))
+      {}
+DatePickerProps::DatePickerProps(
+    const DatePickerProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    date(convertRawProp(rawProps, "date", sourceProps.date, {0.0})),
+    initialDate(convertRawProp(rawProps, "initialDate", sourceProps.initialDate, {0.0})),
+    locale(convertRawProp(rawProps, "locale", sourceProps.locale, {})),
+    maximumDate(convertRawProp(rawProps, "maximumDate", sourceProps.maximumDate, {0.0})),
+    minimumDate(convertRawProp(rawProps, "minimumDate", sourceProps.minimumDate, {0.0})),
+    minuteInterval(convertRawProp(rawProps, "minuteInterval", sourceProps.minuteInterval, {DatePickerMinuteInterval::MinuteInterval1})),
+    mode(convertRawProp(rawProps, "mode", sourceProps.mode, {DatePickerMode::Date})),
+    timeZoneOffsetInMinutes(convertRawProp(rawProps, "timeZoneOffsetInMinutes", sourceProps.timeZoneOffsetInMinutes, {0.0})),
+    pickerStyle(convertRawProp(rawProps, "pickerStyle", sourceProps.pickerStyle, {DatePickerPickerStyle::Spinner}))
+      {}
+AndroidDrawerLayoutProps::AndroidDrawerLayoutProps(
+    const AndroidDrawerLayoutProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    keyboardDismissMode(convertRawProp(rawProps, "keyboardDismissMode", sourceProps.keyboardDismissMode, {AndroidDrawerLayoutKeyboardDismissMode::None})),
+    drawerBackgroundColor(convertRawProp(rawProps, "drawerBackgroundColor", sourceProps.drawerBackgroundColor, {})),
+    drawerPosition(convertRawProp(rawProps, "drawerPosition", sourceProps.drawerPosition, {AndroidDrawerLayoutDrawerPosition::Left})),
+    drawerWidth(convertRawProp(rawProps, "drawerWidth", sourceProps.drawerWidth, {})),
+    drawerLockMode(convertRawProp(rawProps, "drawerLockMode", sourceProps.drawerLockMode, {AndroidDrawerLayoutDrawerLockMode::Unlocked})),
+    statusBarBackgroundColor(convertRawProp(rawProps, "statusBarBackgroundColor", sourceProps.statusBarBackgroundColor, {}))
+      {}
+RCTMaskedViewProps::RCTMaskedViewProps(
+    const RCTMaskedViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps)
+
+    
+      {}
+AndroidProgressBarProps::AndroidProgressBarProps(
+    const AndroidProgressBarProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    styleAttr(convertRawProp(rawProps, "styleAttr", sourceProps.styleAttr, {})),
+    typeAttr(convertRawProp(rawProps, "typeAttr", sourceProps.typeAttr, {})),
+    indeterminate(convertRawProp(rawProps, "indeterminate", sourceProps.indeterminate, {false})),
+    progress(convertRawProp(rawProps, "progress", sourceProps.progress, {0.0})),
+    animating(convertRawProp(rawProps, "animating", sourceProps.animating, {true})),
+    color(convertRawProp(rawProps, "color", sourceProps.color, {})),
+    testID(convertRawProp(rawProps, "testID", sourceProps.testID, {""}))
+      {}
+RCTProgressViewProps::RCTProgressViewProps(
+    const RCTProgressViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    progressViewStyle(convertRawProp(rawProps, "progressViewStyle", sourceProps.progressViewStyle, {RCTProgressViewProgressViewStyle::Default})),
+    progress(convertRawProp(rawProps, "progress", sourceProps.progress, {0.0})),
+    progressTintColor(convertRawProp(rawProps, "progressTintColor", sourceProps.progressTintColor, {})),
+    trackTintColor(convertRawProp(rawProps, "trackTintColor", sourceProps.trackTintColor, {})),
+    progressImage(convertRawProp(rawProps, "progressImage", sourceProps.progressImage, {})),
+    trackImage(convertRawProp(rawProps, "trackImage", sourceProps.trackImage, {}))
+      {}
+AndroidSwipeRefreshLayoutProps::AndroidSwipeRefreshLayoutProps(
+    const AndroidSwipeRefreshLayoutProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    enabled(convertRawProp(rawProps, "enabled", sourceProps.enabled, {true})),
+    colors(convertRawProp(rawProps, "colors", sourceProps.colors, {})),
+    progressBackgroundColor(convertRawProp(rawProps, "progressBackgroundColor", sourceProps.progressBackgroundColor, {})),
+    size(convertRawProp(rawProps, "size", sourceProps.size, {AndroidSwipeRefreshLayoutSize::Default})),
+    progressViewOffset(convertRawProp(rawProps, "progressViewOffset", sourceProps.progressViewOffset, {0.0})),
+    refreshing(convertRawProp(rawProps, "refreshing", sourceProps.refreshing, {false}))
+      {}
+PullToRefreshViewProps::PullToRefreshViewProps(
+    const PullToRefreshViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    tintColor(convertRawProp(rawProps, "tintColor", sourceProps.tintColor, {})),
+    titleColor(convertRawProp(rawProps, "titleColor", sourceProps.titleColor, {})),
+    title(convertRawProp(rawProps, "title", sourceProps.title, {})),
+    progressViewOffset(convertRawProp(rawProps, "progressViewOffset", sourceProps.progressViewOffset, {0.0})),
+    refreshing(convertRawProp(rawProps, "refreshing", sourceProps.refreshing, {false}))
+      {}
+SafeAreaViewProps::SafeAreaViewProps(
+    const SafeAreaViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    emulateUnlessSupported(convertRawProp(rawProps, "emulateUnlessSupported", sourceProps.emulateUnlessSupported, {false}))
+      {}
+AndroidHorizontalScrollContentViewProps::AndroidHorizontalScrollContentViewProps(
+    const AndroidHorizontalScrollContentViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps)
+
+    
+      {}
+RCTSegmentedControlProps::RCTSegmentedControlProps(
+    const RCTSegmentedControlProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    values(convertRawProp(rawProps, "values", sourceProps.values, {})),
+    selectedIndex(convertRawProp(rawProps, "selectedIndex", sourceProps.selectedIndex, {0})),
+    enabled(convertRawProp(rawProps, "enabled", sourceProps.enabled, {true})),
+    tintColor(convertRawProp(rawProps, "tintColor", sourceProps.tintColor, {})),
+    textColor(convertRawProp(rawProps, "textColor", sourceProps.textColor, {})),
+    backgroundColor(convertRawProp(rawProps, "backgroundColor", sourceProps.backgroundColor, {})),
+    momentary(convertRawProp(rawProps, "momentary", sourceProps.momentary, {false}))
+      {}
+SliderProps::SliderProps(
+    const SliderProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    disabled(convertRawProp(rawProps, "disabled", sourceProps.disabled, {false})),
+    enabled(convertRawProp(rawProps, "enabled", sourceProps.enabled, {true})),
+    maximumTrackImage(convertRawProp(rawProps, "maximumTrackImage", sourceProps.maximumTrackImage, {})),
+    maximumTrackTintColor(convertRawProp(rawProps, "maximumTrackTintColor", sourceProps.maximumTrackTintColor, {})),
+    maximumValue(convertRawProp(rawProps, "maximumValue", sourceProps.maximumValue, {1.0})),
+    minimumTrackImage(convertRawProp(rawProps, "minimumTrackImage", sourceProps.minimumTrackImage, {})),
+    minimumTrackTintColor(convertRawProp(rawProps, "minimumTrackTintColor", sourceProps.minimumTrackTintColor, {})),
+    minimumValue(convertRawProp(rawProps, "minimumValue", sourceProps.minimumValue, {0.0})),
+    step(convertRawProp(rawProps, "step", sourceProps.step, {0.0})),
+    testID(convertRawProp(rawProps, "testID", sourceProps.testID, {""})),
+    thumbImage(convertRawProp(rawProps, "thumbImage", sourceProps.thumbImage, {})),
+    thumbTintColor(convertRawProp(rawProps, "thumbTintColor", sourceProps.thumbTintColor, {})),
+    trackImage(convertRawProp(rawProps, "trackImage", sourceProps.trackImage, {})),
+    value(convertRawProp(rawProps, "value", sourceProps.value, {0.0}))
+      {}
+AndroidSwitchProps::AndroidSwitchProps(
+    const AndroidSwitchProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    disabled(convertRawProp(rawProps, "disabled", sourceProps.disabled, {false})),
+    enabled(convertRawProp(rawProps, "enabled", sourceProps.enabled, {true})),
+    thumbColor(convertRawProp(rawProps, "thumbColor", sourceProps.thumbColor, {})),
+    trackColorForFalse(convertRawProp(rawProps, "trackColorForFalse", sourceProps.trackColorForFalse, {})),
+    trackColorForTrue(convertRawProp(rawProps, "trackColorForTrue", sourceProps.trackColorForTrue, {})),
+    value(convertRawProp(rawProps, "value", sourceProps.value, {false})),
+    on(convertRawProp(rawProps, "on", sourceProps.on, {false})),
+    thumbTintColor(convertRawProp(rawProps, "thumbTintColor", sourceProps.thumbTintColor, {})),
+    trackTintColor(convertRawProp(rawProps, "trackTintColor", sourceProps.trackTintColor, {}))
+      {}
+SwitchProps::SwitchProps(
+    const SwitchProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    disabled(convertRawProp(rawProps, "disabled", sourceProps.disabled, {false})),
+    value(convertRawProp(rawProps, "value", sourceProps.value, {false})),
+    tintColor(convertRawProp(rawProps, "tintColor", sourceProps.tintColor, {})),
+    onTintColor(convertRawProp(rawProps, "onTintColor", sourceProps.onTintColor, {})),
+    thumbTintColor(convertRawProp(rawProps, "thumbTintColor", sourceProps.thumbTintColor, {})),
+    thumbColor(convertRawProp(rawProps, "thumbColor", sourceProps.thumbColor, {})),
+    trackColorForFalse(convertRawProp(rawProps, "trackColorForFalse", sourceProps.trackColorForFalse, {})),
+    trackColorForTrue(convertRawProp(rawProps, "trackColorForTrue", sourceProps.trackColorForTrue, {}))
+      {}
+InputAccessoryProps::InputAccessoryProps(
+    const InputAccessoryProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    backgroundColor(convertRawProp(rawProps, "backgroundColor", sourceProps.backgroundColor, {}))
+      {}
+UnimplementedNativeViewProps::UnimplementedNativeViewProps(
+    const UnimplementedNativeViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    name(convertRawProp(rawProps, "name", sourceProps.name, {""}))
+      {}
+ModalHostViewProps::ModalHostViewProps(
+    const ModalHostViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    animationType(convertRawProp(rawProps, "animationType", sourceProps.animationType, {ModalHostViewAnimationType::None})),
+    presentationStyle(convertRawProp(rawProps, "presentationStyle", sourceProps.presentationStyle, {ModalHostViewPresentationStyle::FullScreen})),
+    transparent(convertRawProp(rawProps, "transparent", sourceProps.transparent, {false})),
+    statusBarTranslucent(convertRawProp(rawProps, "statusBarTranslucent", sourceProps.statusBarTranslucent, {false})),
+    hardwareAccelerated(convertRawProp(rawProps, "hardwareAccelerated", sourceProps.hardwareAccelerated, {false})),
+    visible(convertRawProp(rawProps, "visible", sourceProps.visible, {false})),
+    animated(convertRawProp(rawProps, "animated", sourceProps.animated, {false})),
+    supportedOrientations(convertRawProp(rawProps, "supportedOrientations", sourceProps.supportedOrientations, {static_cast<ModalHostViewSupportedOrientationsMask>(ModalHostViewSupportedOrientations::Portrait)})),
+    identifier(convertRawProp(rawProps, "identifier", sourceProps.identifier, {0}))
+      {}
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp.bak b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp.bak
new file mode 100644
index 0000000..fce88f7
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.cpp.bak
@@ -0,0 +1,200 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GeneratePropsCpp.js
+ */
+
+#include <react/renderer/components/FBReactNativeSpec/Props.h>
+#include <react/renderer/components/image/conversions.h>
+#include <react/renderer/core/propsConversions.h>
+
+namespace facebook {
+namespace react {
+
+ActivityIndicatorViewProps::ActivityIndicatorViewProps(
+    const ActivityIndicatorViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    hidesWhenStopped(convertRawProp(rawProps, "hidesWhenStopped", sourceProps.hidesWhenStopped, {false})),
+    animating(convertRawProp(rawProps, "animating", sourceProps.animating, {false})),
+    color(convertRawProp(rawProps, "color", sourceProps.color, {})),
+    size(convertRawProp(rawProps, "size", sourceProps.size, {ActivityIndicatorViewSize::Small}))
+      {}
+DatePickerProps::DatePickerProps(
+    const DatePickerProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    date(convertRawProp(rawProps, "date", sourceProps.date, {0.0})),
+    initialDate(convertRawProp(rawProps, "initialDate", sourceProps.initialDate, {0.0})),
+    locale(convertRawProp(rawProps, "locale", sourceProps.locale, {})),
+    maximumDate(convertRawProp(rawProps, "maximumDate", sourceProps.maximumDate, {0.0})),
+    minimumDate(convertRawProp(rawProps, "minimumDate", sourceProps.minimumDate, {0.0})),
+    minuteInterval(convertRawProp(rawProps, "minuteInterval", sourceProps.minuteInterval, {DatePickerMinuteInterval::MinuteInterval1})),
+    mode(convertRawProp(rawProps, "mode", sourceProps.mode, {DatePickerMode::Date})),
+    timeZoneOffsetInMinutes(convertRawProp(rawProps, "timeZoneOffsetInMinutes", sourceProps.timeZoneOffsetInMinutes, {0.0})),
+    pickerStyle(convertRawProp(rawProps, "pickerStyle", sourceProps.pickerStyle, {DatePickerPickerStyle::Spinner}))
+      {}
+AndroidDrawerLayoutProps::AndroidDrawerLayoutProps(
+    const AndroidDrawerLayoutProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    keyboardDismissMode(convertRawProp(rawProps, "keyboardDismissMode", sourceProps.keyboardDismissMode, {AndroidDrawerLayoutKeyboardDismissMode::None})),
+    drawerBackgroundColor(convertRawProp(rawProps, "drawerBackgroundColor", sourceProps.drawerBackgroundColor, {})),
+    drawerPosition(convertRawProp(rawProps, "drawerPosition", sourceProps.drawerPosition, {AndroidDrawerLayoutDrawerPosition::Left})),
+    drawerWidth(convertRawProp(rawProps, "drawerWidth", sourceProps.drawerWidth, {})),
+    drawerLockMode(convertRawProp(rawProps, "drawerLockMode", sourceProps.drawerLockMode, {AndroidDrawerLayoutDrawerLockMode::Unlocked})),
+    statusBarBackgroundColor(convertRawProp(rawProps, "statusBarBackgroundColor", sourceProps.statusBarBackgroundColor, {}))
+      {}
+RCTMaskedViewProps::RCTMaskedViewProps(
+    const RCTMaskedViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps)
+
+    
+      {}
+AndroidProgressBarProps::AndroidProgressBarProps(
+    const AndroidProgressBarProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    styleAttr(convertRawProp(rawProps, "styleAttr", sourceProps.styleAttr, {})),
+    typeAttr(convertRawProp(rawProps, "typeAttr", sourceProps.typeAttr, {})),
+    indeterminate(convertRawProp(rawProps, "indeterminate", sourceProps.indeterminate, {false})),
+    progress(convertRawProp(rawProps, "progress", sourceProps.progress, {0.0})),
+    animating(convertRawProp(rawProps, "animating", sourceProps.animating, {true})),
+    color(convertRawProp(rawProps, "color", sourceProps.color, {})),
+    testID(convertRawProp(rawProps, "testID", sourceProps.testID, {""}))
+      {}
+RCTProgressViewProps::RCTProgressViewProps(
+    const RCTProgressViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    progressViewStyle(convertRawProp(rawProps, "progressViewStyle", sourceProps.progressViewStyle, {RCTProgressViewProgressViewStyle::Default})),
+    progress(convertRawProp(rawProps, "progress", sourceProps.progress, {0.0})),
+    progressTintColor(convertRawProp(rawProps, "progressTintColor", sourceProps.progressTintColor, {})),
+    trackTintColor(convertRawProp(rawProps, "trackTintColor", sourceProps.trackTintColor, {})),
+    progressImage(convertRawProp(rawProps, "progressImage", sourceProps.progressImage, {})),
+    trackImage(convertRawProp(rawProps, "trackImage", sourceProps.trackImage, {}))
+      {}
+AndroidSwipeRefreshLayoutProps::AndroidSwipeRefreshLayoutProps(
+    const AndroidSwipeRefreshLayoutProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    enabled(convertRawProp(rawProps, "enabled", sourceProps.enabled, {true})),
+    colors(convertRawProp(rawProps, "colors", sourceProps.colors, {})),
+    progressBackgroundColor(convertRawProp(rawProps, "progressBackgroundColor", sourceProps.progressBackgroundColor, {})),
+    size(convertRawProp(rawProps, "size", sourceProps.size, {AndroidSwipeRefreshLayoutSize::Default})),
+    progressViewOffset(convertRawProp(rawProps, "progressViewOffset", sourceProps.progressViewOffset, {0.0})),
+    refreshing(convertRawProp(rawProps, "refreshing", sourceProps.refreshing, {false}))
+      {}
+PullToRefreshViewProps::PullToRefreshViewProps(
+    const PullToRefreshViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    tintColor(convertRawProp(rawProps, "tintColor", sourceProps.tintColor, {})),
+    titleColor(convertRawProp(rawProps, "titleColor", sourceProps.titleColor, {})),
+    title(convertRawProp(rawProps, "title", sourceProps.title, {})),
+    progressViewOffset(convertRawProp(rawProps, "progressViewOffset", sourceProps.progressViewOffset, {0.0})),
+    refreshing(convertRawProp(rawProps, "refreshing", sourceProps.refreshing, {false}))
+      {}
+SafeAreaViewProps::SafeAreaViewProps(
+    const SafeAreaViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    emulateUnlessSupported(convertRawProp(rawProps, "emulateUnlessSupported", sourceProps.emulateUnlessSupported, {false}))
+      {}
+AndroidHorizontalScrollContentViewProps::AndroidHorizontalScrollContentViewProps(
+    const AndroidHorizontalScrollContentViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps)
+
+    
+      {}
+RCTSegmentedControlProps::RCTSegmentedControlProps(
+    const RCTSegmentedControlProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    values(convertRawProp(rawProps, "values", sourceProps.values, {})),
+    selectedIndex(convertRawProp(rawProps, "selectedIndex", sourceProps.selectedIndex, {0})),
+    enabled(convertRawProp(rawProps, "enabled", sourceProps.enabled, {true})),
+    tintColor(convertRawProp(rawProps, "tintColor", sourceProps.tintColor, {})),
+    textColor(convertRawProp(rawProps, "textColor", sourceProps.textColor, {})),
+    backgroundColor(convertRawProp(rawProps, "backgroundColor", sourceProps.backgroundColor, {})),
+    momentary(convertRawProp(rawProps, "momentary", sourceProps.momentary, {false}))
+      {}
+SliderProps::SliderProps(
+    const SliderProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    disabled(convertRawProp(rawProps, "disabled", sourceProps.disabled, {false})),
+    enabled(convertRawProp(rawProps, "enabled", sourceProps.enabled, {true})),
+    maximumTrackImage(convertRawProp(rawProps, "maximumTrackImage", sourceProps.maximumTrackImage, {})),
+    maximumTrackTintColor(convertRawProp(rawProps, "maximumTrackTintColor", sourceProps.maximumTrackTintColor, {})),
+    maximumValue(convertRawProp(rawProps, "maximumValue", sourceProps.maximumValue, {1.0})),
+    minimumTrackImage(convertRawProp(rawProps, "minimumTrackImage", sourceProps.minimumTrackImage, {})),
+    minimumTrackTintColor(convertRawProp(rawProps, "minimumTrackTintColor", sourceProps.minimumTrackTintColor, {})),
+    minimumValue(convertRawProp(rawProps, "minimumValue", sourceProps.minimumValue, {0.0})),
+    step(convertRawProp(rawProps, "step", sourceProps.step, {0.0})),
+    testID(convertRawProp(rawProps, "testID", sourceProps.testID, {""})),
+    thumbImage(convertRawProp(rawProps, "thumbImage", sourceProps.thumbImage, {})),
+    thumbTintColor(convertRawProp(rawProps, "thumbTintColor", sourceProps.thumbTintColor, {})),
+    trackImage(convertRawProp(rawProps, "trackImage", sourceProps.trackImage, {})),
+    value(convertRawProp(rawProps, "value", sourceProps.value, {0.0}))
+      {}
+AndroidSwitchProps::AndroidSwitchProps(
+    const AndroidSwitchProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    disabled(convertRawProp(rawProps, "disabled", sourceProps.disabled, {false})),
+    enabled(convertRawProp(rawProps, "enabled", sourceProps.enabled, {true})),
+    thumbColor(convertRawProp(rawProps, "thumbColor", sourceProps.thumbColor, {})),
+    trackColorForFalse(convertRawProp(rawProps, "trackColorForFalse", sourceProps.trackColorForFalse, {})),
+    trackColorForTrue(convertRawProp(rawProps, "trackColorForTrue", sourceProps.trackColorForTrue, {})),
+    value(convertRawProp(rawProps, "value", sourceProps.value, {false})),
+    on(convertRawProp(rawProps, "on", sourceProps.on, {false})),
+    thumbTintColor(convertRawProp(rawProps, "thumbTintColor", sourceProps.thumbTintColor, {})),
+    trackTintColor(convertRawProp(rawProps, "trackTintColor", sourceProps.trackTintColor, {}))
+      {}
+SwitchProps::SwitchProps(
+    const SwitchProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    disabled(convertRawProp(rawProps, "disabled", sourceProps.disabled, {false})),
+    value(convertRawProp(rawProps, "value", sourceProps.value, {false})),
+    tintColor(convertRawProp(rawProps, "tintColor", sourceProps.tintColor, {})),
+    onTintColor(convertRawProp(rawProps, "onTintColor", sourceProps.onTintColor, {})),
+    thumbTintColor(convertRawProp(rawProps, "thumbTintColor", sourceProps.thumbTintColor, {})),
+    thumbColor(convertRawProp(rawProps, "thumbColor", sourceProps.thumbColor, {})),
+    trackColorForFalse(convertRawProp(rawProps, "trackColorForFalse", sourceProps.trackColorForFalse, {})),
+    trackColorForTrue(convertRawProp(rawProps, "trackColorForTrue", sourceProps.trackColorForTrue, {}))
+      {}
+InputAccessoryProps::InputAccessoryProps(
+    const InputAccessoryProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    backgroundColor(convertRawProp(rawProps, "backgroundColor", sourceProps.backgroundColor, {}))
+      {}
+UnimplementedNativeViewProps::UnimplementedNativeViewProps(
+    const UnimplementedNativeViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    name(convertRawProp(rawProps, "name", sourceProps.name, {""}))
+      {}
+ModalHostViewProps::ModalHostViewProps(
+    const ModalHostViewProps &sourceProps,
+    const RawProps &rawProps): ViewProps(sourceProps, rawProps),
+
+    animationType(convertRawProp(rawProps, "animationType", sourceProps.animationType, {ModalHostViewAnimationType::None})),
+    presentationStyle(convertRawProp(rawProps, "presentationStyle", sourceProps.presentationStyle, {ModalHostViewPresentationStyle::FullScreen})),
+    transparent(convertRawProp(rawProps, "transparent", sourceProps.transparent, {false})),
+    statusBarTranslucent(convertRawProp(rawProps, "statusBarTranslucent", sourceProps.statusBarTranslucent, {false})),
+    hardwareAccelerated(convertRawProp(rawProps, "hardwareAccelerated", sourceProps.hardwareAccelerated, {false})),
+    visible(convertRawProp(rawProps, "visible", sourceProps.visible, {false})),
+    animated(convertRawProp(rawProps, "animated", sourceProps.animated, {false})),
+    supportedOrientations(convertRawProp(rawProps, "supportedOrientations", sourceProps.supportedOrientations, {static_cast<ModalHostViewSupportedOrientationsMask>(ModalHostViewSupportedOrientations::Portrait)})),
+    identifier(convertRawProp(rawProps, "identifier", sourceProps.identifier, {0}))
+      {}
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h
new file mode 100644
index 0000000..feac387
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h
@@ -0,0 +1,575 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GeneratePropsH.js
+ */
+#pragma once
+
+#include <cinttypes>
+#include <react/renderer/components/view/ViewProps.h>
+#include <react/renderer/graphics/Color.h>
+#include <react/renderer/imagemanager/primitives.h>
+#include <vector>
+
+namespace facebook {
+namespace react {
+
+enum class ActivityIndicatorViewSize { Small, Large };
+
+static inline void fromRawValue(const RawValue &value, ActivityIndicatorViewSize &result) {
+  auto string = (std::string)value;
+  if (string == "small") { result = ActivityIndicatorViewSize::Small; return; }
+  if (string == "large") { result = ActivityIndicatorViewSize::Large; return; }
+  abort();
+}
+
+static inline std::string toString(const ActivityIndicatorViewSize &value) {
+  switch (value) {
+    case ActivityIndicatorViewSize::Small: return "small";
+    case ActivityIndicatorViewSize::Large: return "large";
+  }
+}
+
+class ActivityIndicatorViewProps final : public ViewProps {
+ public:
+  ActivityIndicatorViewProps() = default;
+  ActivityIndicatorViewProps(const ActivityIndicatorViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool hidesWhenStopped{false};
+  bool animating{false};
+  SharedColor color{};
+  ActivityIndicatorViewSize size{ActivityIndicatorViewSize::Small};
+};
+
+enum class DatePickerMinuteInterval { MinuteInterval1 = 1, MinuteInterval2 = 2, MinuteInterval3 = 3, MinuteInterval4 = 4, MinuteInterval5 = 5, MinuteInterval6 = 6, MinuteInterval10 = 10, MinuteInterval12 = 12, MinuteInterval15 = 15, MinuteInterval20 = 20, MinuteInterval30 = 30 };
+
+static inline void fromRawValue(const RawValue &value, DatePickerMinuteInterval &result) {
+  assert(value.hasType<int>());
+  auto integerValue = (int)value;
+  switch (integerValue) {
+    case 1:
+      result = DatePickerMinuteInterval::MinuteInterval1;
+      return;
+    case 2:
+      result = DatePickerMinuteInterval::MinuteInterval2;
+      return;
+    case 3:
+      result = DatePickerMinuteInterval::MinuteInterval3;
+      return;
+    case 4:
+      result = DatePickerMinuteInterval::MinuteInterval4;
+      return;
+    case 5:
+      result = DatePickerMinuteInterval::MinuteInterval5;
+      return;
+    case 6:
+      result = DatePickerMinuteInterval::MinuteInterval6;
+      return;
+    case 10:
+      result = DatePickerMinuteInterval::MinuteInterval10;
+      return;
+    case 12:
+      result = DatePickerMinuteInterval::MinuteInterval12;
+      return;
+    case 15:
+      result = DatePickerMinuteInterval::MinuteInterval15;
+      return;
+    case 20:
+      result = DatePickerMinuteInterval::MinuteInterval20;
+      return;
+    case 30:
+      result = DatePickerMinuteInterval::MinuteInterval30;
+      return;
+  }
+  abort();
+}
+
+static inline std::string toString(const DatePickerMinuteInterval &value) {
+  switch (value) {
+    case DatePickerMinuteInterval::MinuteInterval1: return "1";
+    case DatePickerMinuteInterval::MinuteInterval2: return "2";
+    case DatePickerMinuteInterval::MinuteInterval3: return "3";
+    case DatePickerMinuteInterval::MinuteInterval4: return "4";
+    case DatePickerMinuteInterval::MinuteInterval5: return "5";
+    case DatePickerMinuteInterval::MinuteInterval6: return "6";
+    case DatePickerMinuteInterval::MinuteInterval10: return "10";
+    case DatePickerMinuteInterval::MinuteInterval12: return "12";
+    case DatePickerMinuteInterval::MinuteInterval15: return "15";
+    case DatePickerMinuteInterval::MinuteInterval20: return "20";
+    case DatePickerMinuteInterval::MinuteInterval30: return "30";
+  }
+}
+enum class DatePickerMode { Date, Time, Datetime };
+
+static inline void fromRawValue(const RawValue &value, DatePickerMode &result) {
+  auto string = (std::string)value;
+  if (string == "date") { result = DatePickerMode::Date; return; }
+  if (string == "time") { result = DatePickerMode::Time; return; }
+  if (string == "datetime") { result = DatePickerMode::Datetime; return; }
+  abort();
+}
+
+static inline std::string toString(const DatePickerMode &value) {
+  switch (value) {
+    case DatePickerMode::Date: return "date";
+    case DatePickerMode::Time: return "time";
+    case DatePickerMode::Datetime: return "datetime";
+  }
+}
+enum class DatePickerPickerStyle { Compact, Spinner, Inline };
+
+static inline void fromRawValue(const RawValue &value, DatePickerPickerStyle &result) {
+  auto string = (std::string)value;
+  if (string == "compact") { result = DatePickerPickerStyle::Compact; return; }
+  if (string == "spinner") { result = DatePickerPickerStyle::Spinner; return; }
+  if (string == "inline") { result = DatePickerPickerStyle::Inline; return; }
+  abort();
+}
+
+static inline std::string toString(const DatePickerPickerStyle &value) {
+  switch (value) {
+    case DatePickerPickerStyle::Compact: return "compact";
+    case DatePickerPickerStyle::Spinner: return "spinner";
+    case DatePickerPickerStyle::Inline: return "inline";
+  }
+}
+
+class DatePickerProps final : public ViewProps {
+ public:
+  DatePickerProps() = default;
+  DatePickerProps(const DatePickerProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  Float date{0.0};
+  Float initialDate{0.0};
+  std::string locale{};
+  Float maximumDate{0.0};
+  Float minimumDate{0.0};
+  DatePickerMinuteInterval minuteInterval{DatePickerMinuteInterval::MinuteInterval1};
+  DatePickerMode mode{DatePickerMode::Date};
+  Float timeZoneOffsetInMinutes{0.0};
+  DatePickerPickerStyle pickerStyle{DatePickerPickerStyle::Spinner};
+};
+
+enum class AndroidDrawerLayoutKeyboardDismissMode { None, OnDrag };
+
+static inline void fromRawValue(const RawValue &value, AndroidDrawerLayoutKeyboardDismissMode &result) {
+  auto string = (std::string)value;
+  if (string == "none") { result = AndroidDrawerLayoutKeyboardDismissMode::None; return; }
+  if (string == "on-drag") { result = AndroidDrawerLayoutKeyboardDismissMode::OnDrag; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutKeyboardDismissMode &value) {
+  switch (value) {
+    case AndroidDrawerLayoutKeyboardDismissMode::None: return "none";
+    case AndroidDrawerLayoutKeyboardDismissMode::OnDrag: return "on-drag";
+  }
+}
+enum class AndroidDrawerLayoutDrawerPosition { Left, Right };
+
+static inline void fromRawValue(const RawValue &value, AndroidDrawerLayoutDrawerPosition &result) {
+  auto string = (std::string)value;
+  if (string == "left") { result = AndroidDrawerLayoutDrawerPosition::Left; return; }
+  if (string == "right") { result = AndroidDrawerLayoutDrawerPosition::Right; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutDrawerPosition &value) {
+  switch (value) {
+    case AndroidDrawerLayoutDrawerPosition::Left: return "left";
+    case AndroidDrawerLayoutDrawerPosition::Right: return "right";
+  }
+}
+enum class AndroidDrawerLayoutDrawerLockMode { Unlocked, LockedClosed, LockedOpen };
+
+static inline void fromRawValue(const RawValue &value, AndroidDrawerLayoutDrawerLockMode &result) {
+  auto string = (std::string)value;
+  if (string == "unlocked") { result = AndroidDrawerLayoutDrawerLockMode::Unlocked; return; }
+  if (string == "locked-closed") { result = AndroidDrawerLayoutDrawerLockMode::LockedClosed; return; }
+  if (string == "locked-open") { result = AndroidDrawerLayoutDrawerLockMode::LockedOpen; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutDrawerLockMode &value) {
+  switch (value) {
+    case AndroidDrawerLayoutDrawerLockMode::Unlocked: return "unlocked";
+    case AndroidDrawerLayoutDrawerLockMode::LockedClosed: return "locked-closed";
+    case AndroidDrawerLayoutDrawerLockMode::LockedOpen: return "locked-open";
+  }
+}
+
+class AndroidDrawerLayoutProps final : public ViewProps {
+ public:
+  AndroidDrawerLayoutProps() = default;
+  AndroidDrawerLayoutProps(const AndroidDrawerLayoutProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  AndroidDrawerLayoutKeyboardDismissMode keyboardDismissMode{AndroidDrawerLayoutKeyboardDismissMode::None};
+  SharedColor drawerBackgroundColor{};
+  AndroidDrawerLayoutDrawerPosition drawerPosition{AndroidDrawerLayoutDrawerPosition::Left};
+  Float drawerWidth{};
+  AndroidDrawerLayoutDrawerLockMode drawerLockMode{AndroidDrawerLayoutDrawerLockMode::Unlocked};
+  SharedColor statusBarBackgroundColor{};
+};
+
+class RCTMaskedViewProps final : public ViewProps {
+ public:
+  RCTMaskedViewProps() = default;
+  RCTMaskedViewProps(const RCTMaskedViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  
+};
+
+class AndroidProgressBarProps final : public ViewProps {
+ public:
+  AndroidProgressBarProps() = default;
+  AndroidProgressBarProps(const AndroidProgressBarProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::string styleAttr{};
+  std::string typeAttr{};
+  bool indeterminate{false};
+  double progress{0.0};
+  bool animating{true};
+  SharedColor color{};
+  std::string testID{""};
+};
+
+enum class RCTProgressViewProgressViewStyle { Default, Bar };
+
+static inline void fromRawValue(const RawValue &value, RCTProgressViewProgressViewStyle &result) {
+  auto string = (std::string)value;
+  if (string == "default") { result = RCTProgressViewProgressViewStyle::Default; return; }
+  if (string == "bar") { result = RCTProgressViewProgressViewStyle::Bar; return; }
+  abort();
+}
+
+static inline std::string toString(const RCTProgressViewProgressViewStyle &value) {
+  switch (value) {
+    case RCTProgressViewProgressViewStyle::Default: return "default";
+    case RCTProgressViewProgressViewStyle::Bar: return "bar";
+  }
+}
+
+class RCTProgressViewProps final : public ViewProps {
+ public:
+  RCTProgressViewProps() = default;
+  RCTProgressViewProps(const RCTProgressViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  RCTProgressViewProgressViewStyle progressViewStyle{RCTProgressViewProgressViewStyle::Default};
+  Float progress{0.0};
+  SharedColor progressTintColor{};
+  SharedColor trackTintColor{};
+  ImageSource progressImage{};
+  ImageSource trackImage{};
+};
+
+enum class AndroidSwipeRefreshLayoutSize { Default, Large };
+
+static inline void fromRawValue(const RawValue &value, AndroidSwipeRefreshLayoutSize &result) {
+  auto string = (std::string)value;
+  if (string == "default") { result = AndroidSwipeRefreshLayoutSize::Default; return; }
+  if (string == "large") { result = AndroidSwipeRefreshLayoutSize::Large; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidSwipeRefreshLayoutSize &value) {
+  switch (value) {
+    case AndroidSwipeRefreshLayoutSize::Default: return "default";
+    case AndroidSwipeRefreshLayoutSize::Large: return "large";
+  }
+}
+
+class AndroidSwipeRefreshLayoutProps final : public ViewProps {
+ public:
+  AndroidSwipeRefreshLayoutProps() = default;
+  AndroidSwipeRefreshLayoutProps(const AndroidSwipeRefreshLayoutProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool enabled{true};
+  std::vector<SharedColor> colors{};
+  SharedColor progressBackgroundColor{};
+  AndroidSwipeRefreshLayoutSize size{AndroidSwipeRefreshLayoutSize::Default};
+  Float progressViewOffset{0.0};
+  bool refreshing{false};
+};
+
+class PullToRefreshViewProps final : public ViewProps {
+ public:
+  PullToRefreshViewProps() = default;
+  PullToRefreshViewProps(const PullToRefreshViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  SharedColor tintColor{};
+  SharedColor titleColor{};
+  std::string title{};
+  Float progressViewOffset{0.0};
+  bool refreshing{false};
+};
+
+class SafeAreaViewProps final : public ViewProps {
+ public:
+  SafeAreaViewProps() = default;
+  SafeAreaViewProps(const SafeAreaViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool emulateUnlessSupported{false};
+};
+
+class AndroidHorizontalScrollContentViewProps final : public ViewProps {
+ public:
+  AndroidHorizontalScrollContentViewProps() = default;
+  AndroidHorizontalScrollContentViewProps(const AndroidHorizontalScrollContentViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  
+};
+
+class RCTSegmentedControlProps final : public ViewProps {
+ public:
+  RCTSegmentedControlProps() = default;
+  RCTSegmentedControlProps(const RCTSegmentedControlProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::vector<std::string> values{};
+  int selectedIndex{0};
+  bool enabled{true};
+  SharedColor tintColor{};
+  SharedColor textColor{};
+  SharedColor backgroundColor{};
+  bool momentary{false};
+};
+
+class SliderProps final : public ViewProps {
+ public:
+  SliderProps() = default;
+  SliderProps(const SliderProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool disabled{false};
+  bool enabled{true};
+  ImageSource maximumTrackImage{};
+  SharedColor maximumTrackTintColor{};
+  double maximumValue{1.0};
+  ImageSource minimumTrackImage{};
+  SharedColor minimumTrackTintColor{};
+  double minimumValue{0.0};
+  double step{0.0};
+  std::string testID{""};
+  ImageSource thumbImage{};
+  SharedColor thumbTintColor{};
+  ImageSource trackImage{};
+  double value{0.0};
+};
+
+class AndroidSwitchProps final : public ViewProps {
+ public:
+  AndroidSwitchProps() = default;
+  AndroidSwitchProps(const AndroidSwitchProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool disabled{false};
+  bool enabled{true};
+  SharedColor thumbColor{};
+  SharedColor trackColorForFalse{};
+  SharedColor trackColorForTrue{};
+  bool value{false};
+  bool on{false};
+  SharedColor thumbTintColor{};
+  SharedColor trackTintColor{};
+};
+
+class SwitchProps final : public ViewProps {
+ public:
+  SwitchProps() = default;
+  SwitchProps(const SwitchProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool disabled{false};
+  bool value{false};
+  SharedColor tintColor{};
+  SharedColor onTintColor{};
+  SharedColor thumbTintColor{};
+  SharedColor thumbColor{};
+  SharedColor trackColorForFalse{};
+  SharedColor trackColorForTrue{};
+};
+
+class InputAccessoryProps final : public ViewProps {
+ public:
+  InputAccessoryProps() = default;
+  InputAccessoryProps(const InputAccessoryProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  SharedColor backgroundColor{};
+};
+
+class UnimplementedNativeViewProps final : public ViewProps {
+ public:
+  UnimplementedNativeViewProps() = default;
+  UnimplementedNativeViewProps(const UnimplementedNativeViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::string name{""};
+};
+
+enum class ModalHostViewAnimationType { None, Slide, Fade };
+
+static inline void fromRawValue(const RawValue &value, ModalHostViewAnimationType &result) {
+  auto string = (std::string)value;
+  if (string == "none") { result = ModalHostViewAnimationType::None; return; }
+  if (string == "slide") { result = ModalHostViewAnimationType::Slide; return; }
+  if (string == "fade") { result = ModalHostViewAnimationType::Fade; return; }
+  abort();
+}
+
+static inline std::string toString(const ModalHostViewAnimationType &value) {
+  switch (value) {
+    case ModalHostViewAnimationType::None: return "none";
+    case ModalHostViewAnimationType::Slide: return "slide";
+    case ModalHostViewAnimationType::Fade: return "fade";
+  }
+}
+enum class ModalHostViewPresentationStyle { FullScreen, PageSheet, FormSheet, OverFullScreen };
+
+static inline void fromRawValue(const RawValue &value, ModalHostViewPresentationStyle &result) {
+  auto string = (std::string)value;
+  if (string == "fullScreen") { result = ModalHostViewPresentationStyle::FullScreen; return; }
+  if (string == "pageSheet") { result = ModalHostViewPresentationStyle::PageSheet; return; }
+  if (string == "formSheet") { result = ModalHostViewPresentationStyle::FormSheet; return; }
+  if (string == "overFullScreen") { result = ModalHostViewPresentationStyle::OverFullScreen; return; }
+  abort();
+}
+
+static inline std::string toString(const ModalHostViewPresentationStyle &value) {
+  switch (value) {
+    case ModalHostViewPresentationStyle::FullScreen: return "fullScreen";
+    case ModalHostViewPresentationStyle::PageSheet: return "pageSheet";
+    case ModalHostViewPresentationStyle::FormSheet: return "formSheet";
+    case ModalHostViewPresentationStyle::OverFullScreen: return "overFullScreen";
+  }
+}
+using ModalHostViewSupportedOrientationsMask = uint32_t;
+
+enum class ModalHostViewSupportedOrientations: ModalHostViewSupportedOrientationsMask {
+  Portrait = 1 << 0,
+  PortraitUpsideDown = 1 << 1,
+  Landscape = 1 << 2,
+  LandscapeLeft = 1 << 3,
+  LandscapeRight = 1 << 4
+};
+
+constexpr bool operator&(
+  ModalHostViewSupportedOrientationsMask const lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  return lhs & static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+constexpr ModalHostViewSupportedOrientationsMask operator|(
+  ModalHostViewSupportedOrientationsMask const lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  return lhs | static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+constexpr void operator|=(
+  ModalHostViewSupportedOrientationsMask &lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  lhs = lhs | static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+static inline void fromRawValue(const RawValue &value, ModalHostViewSupportedOrientationsMask &result) {
+  auto items = std::vector<std::string>{value};
+  for (const auto &item : items) {
+    if (item == "portrait") {
+      result |= ModalHostViewSupportedOrientations::Portrait;
+      continue;
+    }
+    if (item == "portrait-upside-down") {
+      result |= ModalHostViewSupportedOrientations::PortraitUpsideDown;
+      continue;
+    }
+    if (item == "landscape") {
+      result |= ModalHostViewSupportedOrientations::Landscape;
+      continue;
+    }
+    if (item == "landscape-left") {
+      result |= ModalHostViewSupportedOrientations::LandscapeLeft;
+      continue;
+    }
+    if (item == "landscape-right") {
+      result |= ModalHostViewSupportedOrientations::LandscapeRight;
+      continue;
+    }
+    abort();
+  }
+}
+
+static inline std::string toString(const ModalHostViewSupportedOrientationsMask &value) {
+    auto result = std::string{};
+    auto separator = std::string{", "};
+
+    if (value & ModalHostViewSupportedOrientations::Portrait) {
+      result += "portrait" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::PortraitUpsideDown) {
+      result += "portrait-upside-down" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::Landscape) {
+      result += "landscape" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::LandscapeLeft) {
+      result += "landscape-left" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::LandscapeRight) {
+      result += "landscape-right" + separator;
+    }
+    if (!result.empty()) {
+      result.erase(result.length() - separator.length());
+    }
+    return result;
+}
+
+class ModalHostViewProps final : public ViewProps {
+ public:
+  ModalHostViewProps() = default;
+  ModalHostViewProps(const ModalHostViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  ModalHostViewAnimationType animationType{ModalHostViewAnimationType::None};
+  ModalHostViewPresentationStyle presentationStyle{ModalHostViewPresentationStyle::FullScreen};
+  bool transparent{false};
+  bool statusBarTranslucent{false};
+  bool hardwareAccelerated{false};
+  bool visible{false};
+  bool animated{false};
+  ModalHostViewSupportedOrientationsMask supportedOrientations{static_cast<ModalHostViewSupportedOrientationsMask>(ModalHostViewSupportedOrientations::Portrait)};
+  int identifier{0};
+};
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h.bak b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h.bak
new file mode 100644
index 0000000..feac387
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/Props.h.bak
@@ -0,0 +1,575 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GeneratePropsH.js
+ */
+#pragma once
+
+#include <cinttypes>
+#include <react/renderer/components/view/ViewProps.h>
+#include <react/renderer/graphics/Color.h>
+#include <react/renderer/imagemanager/primitives.h>
+#include <vector>
+
+namespace facebook {
+namespace react {
+
+enum class ActivityIndicatorViewSize { Small, Large };
+
+static inline void fromRawValue(const RawValue &value, ActivityIndicatorViewSize &result) {
+  auto string = (std::string)value;
+  if (string == "small") { result = ActivityIndicatorViewSize::Small; return; }
+  if (string == "large") { result = ActivityIndicatorViewSize::Large; return; }
+  abort();
+}
+
+static inline std::string toString(const ActivityIndicatorViewSize &value) {
+  switch (value) {
+    case ActivityIndicatorViewSize::Small: return "small";
+    case ActivityIndicatorViewSize::Large: return "large";
+  }
+}
+
+class ActivityIndicatorViewProps final : public ViewProps {
+ public:
+  ActivityIndicatorViewProps() = default;
+  ActivityIndicatorViewProps(const ActivityIndicatorViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool hidesWhenStopped{false};
+  bool animating{false};
+  SharedColor color{};
+  ActivityIndicatorViewSize size{ActivityIndicatorViewSize::Small};
+};
+
+enum class DatePickerMinuteInterval { MinuteInterval1 = 1, MinuteInterval2 = 2, MinuteInterval3 = 3, MinuteInterval4 = 4, MinuteInterval5 = 5, MinuteInterval6 = 6, MinuteInterval10 = 10, MinuteInterval12 = 12, MinuteInterval15 = 15, MinuteInterval20 = 20, MinuteInterval30 = 30 };
+
+static inline void fromRawValue(const RawValue &value, DatePickerMinuteInterval &result) {
+  assert(value.hasType<int>());
+  auto integerValue = (int)value;
+  switch (integerValue) {
+    case 1:
+      result = DatePickerMinuteInterval::MinuteInterval1;
+      return;
+    case 2:
+      result = DatePickerMinuteInterval::MinuteInterval2;
+      return;
+    case 3:
+      result = DatePickerMinuteInterval::MinuteInterval3;
+      return;
+    case 4:
+      result = DatePickerMinuteInterval::MinuteInterval4;
+      return;
+    case 5:
+      result = DatePickerMinuteInterval::MinuteInterval5;
+      return;
+    case 6:
+      result = DatePickerMinuteInterval::MinuteInterval6;
+      return;
+    case 10:
+      result = DatePickerMinuteInterval::MinuteInterval10;
+      return;
+    case 12:
+      result = DatePickerMinuteInterval::MinuteInterval12;
+      return;
+    case 15:
+      result = DatePickerMinuteInterval::MinuteInterval15;
+      return;
+    case 20:
+      result = DatePickerMinuteInterval::MinuteInterval20;
+      return;
+    case 30:
+      result = DatePickerMinuteInterval::MinuteInterval30;
+      return;
+  }
+  abort();
+}
+
+static inline std::string toString(const DatePickerMinuteInterval &value) {
+  switch (value) {
+    case DatePickerMinuteInterval::MinuteInterval1: return "1";
+    case DatePickerMinuteInterval::MinuteInterval2: return "2";
+    case DatePickerMinuteInterval::MinuteInterval3: return "3";
+    case DatePickerMinuteInterval::MinuteInterval4: return "4";
+    case DatePickerMinuteInterval::MinuteInterval5: return "5";
+    case DatePickerMinuteInterval::MinuteInterval6: return "6";
+    case DatePickerMinuteInterval::MinuteInterval10: return "10";
+    case DatePickerMinuteInterval::MinuteInterval12: return "12";
+    case DatePickerMinuteInterval::MinuteInterval15: return "15";
+    case DatePickerMinuteInterval::MinuteInterval20: return "20";
+    case DatePickerMinuteInterval::MinuteInterval30: return "30";
+  }
+}
+enum class DatePickerMode { Date, Time, Datetime };
+
+static inline void fromRawValue(const RawValue &value, DatePickerMode &result) {
+  auto string = (std::string)value;
+  if (string == "date") { result = DatePickerMode::Date; return; }
+  if (string == "time") { result = DatePickerMode::Time; return; }
+  if (string == "datetime") { result = DatePickerMode::Datetime; return; }
+  abort();
+}
+
+static inline std::string toString(const DatePickerMode &value) {
+  switch (value) {
+    case DatePickerMode::Date: return "date";
+    case DatePickerMode::Time: return "time";
+    case DatePickerMode::Datetime: return "datetime";
+  }
+}
+enum class DatePickerPickerStyle { Compact, Spinner, Inline };
+
+static inline void fromRawValue(const RawValue &value, DatePickerPickerStyle &result) {
+  auto string = (std::string)value;
+  if (string == "compact") { result = DatePickerPickerStyle::Compact; return; }
+  if (string == "spinner") { result = DatePickerPickerStyle::Spinner; return; }
+  if (string == "inline") { result = DatePickerPickerStyle::Inline; return; }
+  abort();
+}
+
+static inline std::string toString(const DatePickerPickerStyle &value) {
+  switch (value) {
+    case DatePickerPickerStyle::Compact: return "compact";
+    case DatePickerPickerStyle::Spinner: return "spinner";
+    case DatePickerPickerStyle::Inline: return "inline";
+  }
+}
+
+class DatePickerProps final : public ViewProps {
+ public:
+  DatePickerProps() = default;
+  DatePickerProps(const DatePickerProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  Float date{0.0};
+  Float initialDate{0.0};
+  std::string locale{};
+  Float maximumDate{0.0};
+  Float minimumDate{0.0};
+  DatePickerMinuteInterval minuteInterval{DatePickerMinuteInterval::MinuteInterval1};
+  DatePickerMode mode{DatePickerMode::Date};
+  Float timeZoneOffsetInMinutes{0.0};
+  DatePickerPickerStyle pickerStyle{DatePickerPickerStyle::Spinner};
+};
+
+enum class AndroidDrawerLayoutKeyboardDismissMode { None, OnDrag };
+
+static inline void fromRawValue(const RawValue &value, AndroidDrawerLayoutKeyboardDismissMode &result) {
+  auto string = (std::string)value;
+  if (string == "none") { result = AndroidDrawerLayoutKeyboardDismissMode::None; return; }
+  if (string == "on-drag") { result = AndroidDrawerLayoutKeyboardDismissMode::OnDrag; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutKeyboardDismissMode &value) {
+  switch (value) {
+    case AndroidDrawerLayoutKeyboardDismissMode::None: return "none";
+    case AndroidDrawerLayoutKeyboardDismissMode::OnDrag: return "on-drag";
+  }
+}
+enum class AndroidDrawerLayoutDrawerPosition { Left, Right };
+
+static inline void fromRawValue(const RawValue &value, AndroidDrawerLayoutDrawerPosition &result) {
+  auto string = (std::string)value;
+  if (string == "left") { result = AndroidDrawerLayoutDrawerPosition::Left; return; }
+  if (string == "right") { result = AndroidDrawerLayoutDrawerPosition::Right; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutDrawerPosition &value) {
+  switch (value) {
+    case AndroidDrawerLayoutDrawerPosition::Left: return "left";
+    case AndroidDrawerLayoutDrawerPosition::Right: return "right";
+  }
+}
+enum class AndroidDrawerLayoutDrawerLockMode { Unlocked, LockedClosed, LockedOpen };
+
+static inline void fromRawValue(const RawValue &value, AndroidDrawerLayoutDrawerLockMode &result) {
+  auto string = (std::string)value;
+  if (string == "unlocked") { result = AndroidDrawerLayoutDrawerLockMode::Unlocked; return; }
+  if (string == "locked-closed") { result = AndroidDrawerLayoutDrawerLockMode::LockedClosed; return; }
+  if (string == "locked-open") { result = AndroidDrawerLayoutDrawerLockMode::LockedOpen; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidDrawerLayoutDrawerLockMode &value) {
+  switch (value) {
+    case AndroidDrawerLayoutDrawerLockMode::Unlocked: return "unlocked";
+    case AndroidDrawerLayoutDrawerLockMode::LockedClosed: return "locked-closed";
+    case AndroidDrawerLayoutDrawerLockMode::LockedOpen: return "locked-open";
+  }
+}
+
+class AndroidDrawerLayoutProps final : public ViewProps {
+ public:
+  AndroidDrawerLayoutProps() = default;
+  AndroidDrawerLayoutProps(const AndroidDrawerLayoutProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  AndroidDrawerLayoutKeyboardDismissMode keyboardDismissMode{AndroidDrawerLayoutKeyboardDismissMode::None};
+  SharedColor drawerBackgroundColor{};
+  AndroidDrawerLayoutDrawerPosition drawerPosition{AndroidDrawerLayoutDrawerPosition::Left};
+  Float drawerWidth{};
+  AndroidDrawerLayoutDrawerLockMode drawerLockMode{AndroidDrawerLayoutDrawerLockMode::Unlocked};
+  SharedColor statusBarBackgroundColor{};
+};
+
+class RCTMaskedViewProps final : public ViewProps {
+ public:
+  RCTMaskedViewProps() = default;
+  RCTMaskedViewProps(const RCTMaskedViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  
+};
+
+class AndroidProgressBarProps final : public ViewProps {
+ public:
+  AndroidProgressBarProps() = default;
+  AndroidProgressBarProps(const AndroidProgressBarProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::string styleAttr{};
+  std::string typeAttr{};
+  bool indeterminate{false};
+  double progress{0.0};
+  bool animating{true};
+  SharedColor color{};
+  std::string testID{""};
+};
+
+enum class RCTProgressViewProgressViewStyle { Default, Bar };
+
+static inline void fromRawValue(const RawValue &value, RCTProgressViewProgressViewStyle &result) {
+  auto string = (std::string)value;
+  if (string == "default") { result = RCTProgressViewProgressViewStyle::Default; return; }
+  if (string == "bar") { result = RCTProgressViewProgressViewStyle::Bar; return; }
+  abort();
+}
+
+static inline std::string toString(const RCTProgressViewProgressViewStyle &value) {
+  switch (value) {
+    case RCTProgressViewProgressViewStyle::Default: return "default";
+    case RCTProgressViewProgressViewStyle::Bar: return "bar";
+  }
+}
+
+class RCTProgressViewProps final : public ViewProps {
+ public:
+  RCTProgressViewProps() = default;
+  RCTProgressViewProps(const RCTProgressViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  RCTProgressViewProgressViewStyle progressViewStyle{RCTProgressViewProgressViewStyle::Default};
+  Float progress{0.0};
+  SharedColor progressTintColor{};
+  SharedColor trackTintColor{};
+  ImageSource progressImage{};
+  ImageSource trackImage{};
+};
+
+enum class AndroidSwipeRefreshLayoutSize { Default, Large };
+
+static inline void fromRawValue(const RawValue &value, AndroidSwipeRefreshLayoutSize &result) {
+  auto string = (std::string)value;
+  if (string == "default") { result = AndroidSwipeRefreshLayoutSize::Default; return; }
+  if (string == "large") { result = AndroidSwipeRefreshLayoutSize::Large; return; }
+  abort();
+}
+
+static inline std::string toString(const AndroidSwipeRefreshLayoutSize &value) {
+  switch (value) {
+    case AndroidSwipeRefreshLayoutSize::Default: return "default";
+    case AndroidSwipeRefreshLayoutSize::Large: return "large";
+  }
+}
+
+class AndroidSwipeRefreshLayoutProps final : public ViewProps {
+ public:
+  AndroidSwipeRefreshLayoutProps() = default;
+  AndroidSwipeRefreshLayoutProps(const AndroidSwipeRefreshLayoutProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool enabled{true};
+  std::vector<SharedColor> colors{};
+  SharedColor progressBackgroundColor{};
+  AndroidSwipeRefreshLayoutSize size{AndroidSwipeRefreshLayoutSize::Default};
+  Float progressViewOffset{0.0};
+  bool refreshing{false};
+};
+
+class PullToRefreshViewProps final : public ViewProps {
+ public:
+  PullToRefreshViewProps() = default;
+  PullToRefreshViewProps(const PullToRefreshViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  SharedColor tintColor{};
+  SharedColor titleColor{};
+  std::string title{};
+  Float progressViewOffset{0.0};
+  bool refreshing{false};
+};
+
+class SafeAreaViewProps final : public ViewProps {
+ public:
+  SafeAreaViewProps() = default;
+  SafeAreaViewProps(const SafeAreaViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool emulateUnlessSupported{false};
+};
+
+class AndroidHorizontalScrollContentViewProps final : public ViewProps {
+ public:
+  AndroidHorizontalScrollContentViewProps() = default;
+  AndroidHorizontalScrollContentViewProps(const AndroidHorizontalScrollContentViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  
+};
+
+class RCTSegmentedControlProps final : public ViewProps {
+ public:
+  RCTSegmentedControlProps() = default;
+  RCTSegmentedControlProps(const RCTSegmentedControlProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::vector<std::string> values{};
+  int selectedIndex{0};
+  bool enabled{true};
+  SharedColor tintColor{};
+  SharedColor textColor{};
+  SharedColor backgroundColor{};
+  bool momentary{false};
+};
+
+class SliderProps final : public ViewProps {
+ public:
+  SliderProps() = default;
+  SliderProps(const SliderProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool disabled{false};
+  bool enabled{true};
+  ImageSource maximumTrackImage{};
+  SharedColor maximumTrackTintColor{};
+  double maximumValue{1.0};
+  ImageSource minimumTrackImage{};
+  SharedColor minimumTrackTintColor{};
+  double minimumValue{0.0};
+  double step{0.0};
+  std::string testID{""};
+  ImageSource thumbImage{};
+  SharedColor thumbTintColor{};
+  ImageSource trackImage{};
+  double value{0.0};
+};
+
+class AndroidSwitchProps final : public ViewProps {
+ public:
+  AndroidSwitchProps() = default;
+  AndroidSwitchProps(const AndroidSwitchProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool disabled{false};
+  bool enabled{true};
+  SharedColor thumbColor{};
+  SharedColor trackColorForFalse{};
+  SharedColor trackColorForTrue{};
+  bool value{false};
+  bool on{false};
+  SharedColor thumbTintColor{};
+  SharedColor trackTintColor{};
+};
+
+class SwitchProps final : public ViewProps {
+ public:
+  SwitchProps() = default;
+  SwitchProps(const SwitchProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  bool disabled{false};
+  bool value{false};
+  SharedColor tintColor{};
+  SharedColor onTintColor{};
+  SharedColor thumbTintColor{};
+  SharedColor thumbColor{};
+  SharedColor trackColorForFalse{};
+  SharedColor trackColorForTrue{};
+};
+
+class InputAccessoryProps final : public ViewProps {
+ public:
+  InputAccessoryProps() = default;
+  InputAccessoryProps(const InputAccessoryProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  SharedColor backgroundColor{};
+};
+
+class UnimplementedNativeViewProps final : public ViewProps {
+ public:
+  UnimplementedNativeViewProps() = default;
+  UnimplementedNativeViewProps(const UnimplementedNativeViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  std::string name{""};
+};
+
+enum class ModalHostViewAnimationType { None, Slide, Fade };
+
+static inline void fromRawValue(const RawValue &value, ModalHostViewAnimationType &result) {
+  auto string = (std::string)value;
+  if (string == "none") { result = ModalHostViewAnimationType::None; return; }
+  if (string == "slide") { result = ModalHostViewAnimationType::Slide; return; }
+  if (string == "fade") { result = ModalHostViewAnimationType::Fade; return; }
+  abort();
+}
+
+static inline std::string toString(const ModalHostViewAnimationType &value) {
+  switch (value) {
+    case ModalHostViewAnimationType::None: return "none";
+    case ModalHostViewAnimationType::Slide: return "slide";
+    case ModalHostViewAnimationType::Fade: return "fade";
+  }
+}
+enum class ModalHostViewPresentationStyle { FullScreen, PageSheet, FormSheet, OverFullScreen };
+
+static inline void fromRawValue(const RawValue &value, ModalHostViewPresentationStyle &result) {
+  auto string = (std::string)value;
+  if (string == "fullScreen") { result = ModalHostViewPresentationStyle::FullScreen; return; }
+  if (string == "pageSheet") { result = ModalHostViewPresentationStyle::PageSheet; return; }
+  if (string == "formSheet") { result = ModalHostViewPresentationStyle::FormSheet; return; }
+  if (string == "overFullScreen") { result = ModalHostViewPresentationStyle::OverFullScreen; return; }
+  abort();
+}
+
+static inline std::string toString(const ModalHostViewPresentationStyle &value) {
+  switch (value) {
+    case ModalHostViewPresentationStyle::FullScreen: return "fullScreen";
+    case ModalHostViewPresentationStyle::PageSheet: return "pageSheet";
+    case ModalHostViewPresentationStyle::FormSheet: return "formSheet";
+    case ModalHostViewPresentationStyle::OverFullScreen: return "overFullScreen";
+  }
+}
+using ModalHostViewSupportedOrientationsMask = uint32_t;
+
+enum class ModalHostViewSupportedOrientations: ModalHostViewSupportedOrientationsMask {
+  Portrait = 1 << 0,
+  PortraitUpsideDown = 1 << 1,
+  Landscape = 1 << 2,
+  LandscapeLeft = 1 << 3,
+  LandscapeRight = 1 << 4
+};
+
+constexpr bool operator&(
+  ModalHostViewSupportedOrientationsMask const lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  return lhs & static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+constexpr ModalHostViewSupportedOrientationsMask operator|(
+  ModalHostViewSupportedOrientationsMask const lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  return lhs | static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+constexpr void operator|=(
+  ModalHostViewSupportedOrientationsMask &lhs,
+  enum ModalHostViewSupportedOrientations const rhs) {
+  lhs = lhs | static_cast<ModalHostViewSupportedOrientationsMask>(rhs);
+}
+
+static inline void fromRawValue(const RawValue &value, ModalHostViewSupportedOrientationsMask &result) {
+  auto items = std::vector<std::string>{value};
+  for (const auto &item : items) {
+    if (item == "portrait") {
+      result |= ModalHostViewSupportedOrientations::Portrait;
+      continue;
+    }
+    if (item == "portrait-upside-down") {
+      result |= ModalHostViewSupportedOrientations::PortraitUpsideDown;
+      continue;
+    }
+    if (item == "landscape") {
+      result |= ModalHostViewSupportedOrientations::Landscape;
+      continue;
+    }
+    if (item == "landscape-left") {
+      result |= ModalHostViewSupportedOrientations::LandscapeLeft;
+      continue;
+    }
+    if (item == "landscape-right") {
+      result |= ModalHostViewSupportedOrientations::LandscapeRight;
+      continue;
+    }
+    abort();
+  }
+}
+
+static inline std::string toString(const ModalHostViewSupportedOrientationsMask &value) {
+    auto result = std::string{};
+    auto separator = std::string{", "};
+
+    if (value & ModalHostViewSupportedOrientations::Portrait) {
+      result += "portrait" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::PortraitUpsideDown) {
+      result += "portrait-upside-down" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::Landscape) {
+      result += "landscape" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::LandscapeLeft) {
+      result += "landscape-left" + separator;
+    }
+    if (value & ModalHostViewSupportedOrientations::LandscapeRight) {
+      result += "landscape-right" + separator;
+    }
+    if (!result.empty()) {
+      result.erase(result.length() - separator.length());
+    }
+    return result;
+}
+
+class ModalHostViewProps final : public ViewProps {
+ public:
+  ModalHostViewProps() = default;
+  ModalHostViewProps(const ModalHostViewProps &sourceProps, const RawProps &rawProps);
+
+#pragma mark - Props
+
+  ModalHostViewAnimationType animationType{ModalHostViewAnimationType::None};
+  ModalHostViewPresentationStyle presentationStyle{ModalHostViewPresentationStyle::FullScreen};
+  bool transparent{false};
+  bool statusBarTranslucent{false};
+  bool hardwareAccelerated{false};
+  bool visible{false};
+  bool animated{false};
+  ModalHostViewSupportedOrientationsMask supportedOrientations{static_cast<ModalHostViewSupportedOrientationsMask>(ModalHostViewSupportedOrientations::Portrait)};
+  int identifier{0};
+};
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h
new file mode 100644
index 0000000..be95fd5
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h
@@ -0,0 +1,273 @@
+/**
+* Copyright (c) Facebook, Inc. and its affiliates.
+*
+* This source code is licensed under the MIT license found in the
+* LICENSE file in the root directory of this source tree.
+*
+* @generated by codegen project: GenerateComponentHObjCpp.js
+*/
+
+#import <Foundation/Foundation.h>
+#import <React/RCTDefines.h>
+#import <React/RCTLog.h>
+
+NS_ASSUME_NONNULL_BEGIN
+
+@protocol RCTActivityIndicatorViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTDatePickerViewProtocol <NSObject>
+- (void)setNativeDate:(float)date;
+@end
+
+RCT_EXTERN inline void RCTDatePickerHandleCommand(
+  id<RCTDatePickerViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeDate"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"DatePicker", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"float", @"DatePicker", commandName, @"1st")) {
+    return;
+  }
+#endif
+  float date = [(NSNumber *)arg0 floatValue];
+
+  [componentView setNativeDate:date];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"DatePicker", commandName);
+#endif
+}
+
+@protocol RCTAndroidDrawerLayoutViewProtocol <NSObject>
+- (void)openDrawer;
+- (void)closeDrawer;
+@end
+
+RCT_EXTERN inline void RCTAndroidDrawerLayoutHandleCommand(
+  id<RCTAndroidDrawerLayoutViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"openDrawer"]) {
+#if RCT_DEBUG
+  if ([args count] != 0) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidDrawerLayout", commandName, (int)[args count], 0);
+    return;
+  }
+#endif
+
+  
+
+  [componentView openDrawer];
+  return;
+}
+
+if ([commandName isEqualToString:@"closeDrawer"]) {
+#if RCT_DEBUG
+  if ([args count] != 0) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidDrawerLayout", commandName, (int)[args count], 0);
+    return;
+  }
+#endif
+
+  
+
+  [componentView closeDrawer];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidDrawerLayout", commandName);
+#endif
+}
+
+@protocol RCTRCTMaskedViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidProgressBarViewProtocol <NSObject>
+
+@end
+
+@protocol RCTRCTProgressViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidSwipeRefreshLayoutViewProtocol <NSObject>
+- (void)setNativeRefreshing:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTAndroidSwipeRefreshLayoutHandleCommand(
+  id<RCTAndroidSwipeRefreshLayoutViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeRefreshing"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidSwipeRefreshLayout", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"AndroidSwipeRefreshLayout", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeRefreshing:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidSwipeRefreshLayout", commandName);
+#endif
+}
+
+@protocol RCTPullToRefreshViewViewProtocol <NSObject>
+- (void)setNativeRefreshing:(BOOL)refreshing;
+@end
+
+RCT_EXTERN inline void RCTPullToRefreshViewHandleCommand(
+  id<RCTPullToRefreshViewViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeRefreshing"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"PullToRefreshView", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"PullToRefreshView", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL refreshing = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeRefreshing:refreshing];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"PullToRefreshView", commandName);
+#endif
+}
+
+@protocol RCTSafeAreaViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidHorizontalScrollContentViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTRCTSegmentedControlViewProtocol <NSObject>
+
+@end
+
+@protocol RCTSliderViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidSwitchViewProtocol <NSObject>
+- (void)setNativeValue:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTAndroidSwitchHandleCommand(
+  id<RCTAndroidSwitchViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeValue"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidSwitch", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"AndroidSwitch", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeValue:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidSwitch", commandName);
+#endif
+}
+
+@protocol RCTSwitchViewProtocol <NSObject>
+- (void)setValue:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTSwitchHandleCommand(
+  id<RCTSwitchViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setValue"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"Switch", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"Switch", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setValue:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"Switch", commandName);
+#endif
+}
+
+@protocol RCTInputAccessoryViewProtocol <NSObject>
+
+@end
+
+@protocol RCTUnimplementedNativeViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTModalHostViewViewProtocol <NSObject>
+
+@end
+
+NS_ASSUME_NONNULL_END
\ No newline at end of file
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h.bak b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h.bak
new file mode 100644
index 0000000..be95fd5
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/RCTComponentViewHelpers.h.bak
@@ -0,0 +1,273 @@
+/**
+* Copyright (c) Facebook, Inc. and its affiliates.
+*
+* This source code is licensed under the MIT license found in the
+* LICENSE file in the root directory of this source tree.
+*
+* @generated by codegen project: GenerateComponentHObjCpp.js
+*/
+
+#import <Foundation/Foundation.h>
+#import <React/RCTDefines.h>
+#import <React/RCTLog.h>
+
+NS_ASSUME_NONNULL_BEGIN
+
+@protocol RCTActivityIndicatorViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTDatePickerViewProtocol <NSObject>
+- (void)setNativeDate:(float)date;
+@end
+
+RCT_EXTERN inline void RCTDatePickerHandleCommand(
+  id<RCTDatePickerViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeDate"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"DatePicker", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"float", @"DatePicker", commandName, @"1st")) {
+    return;
+  }
+#endif
+  float date = [(NSNumber *)arg0 floatValue];
+
+  [componentView setNativeDate:date];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"DatePicker", commandName);
+#endif
+}
+
+@protocol RCTAndroidDrawerLayoutViewProtocol <NSObject>
+- (void)openDrawer;
+- (void)closeDrawer;
+@end
+
+RCT_EXTERN inline void RCTAndroidDrawerLayoutHandleCommand(
+  id<RCTAndroidDrawerLayoutViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"openDrawer"]) {
+#if RCT_DEBUG
+  if ([args count] != 0) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidDrawerLayout", commandName, (int)[args count], 0);
+    return;
+  }
+#endif
+
+  
+
+  [componentView openDrawer];
+  return;
+}
+
+if ([commandName isEqualToString:@"closeDrawer"]) {
+#if RCT_DEBUG
+  if ([args count] != 0) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidDrawerLayout", commandName, (int)[args count], 0);
+    return;
+  }
+#endif
+
+  
+
+  [componentView closeDrawer];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidDrawerLayout", commandName);
+#endif
+}
+
+@protocol RCTRCTMaskedViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidProgressBarViewProtocol <NSObject>
+
+@end
+
+@protocol RCTRCTProgressViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidSwipeRefreshLayoutViewProtocol <NSObject>
+- (void)setNativeRefreshing:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTAndroidSwipeRefreshLayoutHandleCommand(
+  id<RCTAndroidSwipeRefreshLayoutViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeRefreshing"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidSwipeRefreshLayout", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"AndroidSwipeRefreshLayout", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeRefreshing:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidSwipeRefreshLayout", commandName);
+#endif
+}
+
+@protocol RCTPullToRefreshViewViewProtocol <NSObject>
+- (void)setNativeRefreshing:(BOOL)refreshing;
+@end
+
+RCT_EXTERN inline void RCTPullToRefreshViewHandleCommand(
+  id<RCTPullToRefreshViewViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeRefreshing"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"PullToRefreshView", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"PullToRefreshView", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL refreshing = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeRefreshing:refreshing];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"PullToRefreshView", commandName);
+#endif
+}
+
+@protocol RCTSafeAreaViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidHorizontalScrollContentViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTRCTSegmentedControlViewProtocol <NSObject>
+
+@end
+
+@protocol RCTSliderViewProtocol <NSObject>
+
+@end
+
+@protocol RCTAndroidSwitchViewProtocol <NSObject>
+- (void)setNativeValue:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTAndroidSwitchHandleCommand(
+  id<RCTAndroidSwitchViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setNativeValue"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"AndroidSwitch", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"AndroidSwitch", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setNativeValue:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"AndroidSwitch", commandName);
+#endif
+}
+
+@protocol RCTSwitchViewProtocol <NSObject>
+- (void)setValue:(BOOL)value;
+@end
+
+RCT_EXTERN inline void RCTSwitchHandleCommand(
+  id<RCTSwitchViewProtocol> componentView,
+  NSString const *commandName,
+  NSArray const *args)
+{
+  if ([commandName isEqualToString:@"setValue"]) {
+#if RCT_DEBUG
+  if ([args count] != 1) {
+    RCTLogError(@"%@ command %@ received %d arguments, expected %d.", @"Switch", commandName, (int)[args count], 1);
+    return;
+  }
+#endif
+
+  NSObject *arg0 = args[0];
+#if RCT_DEBUG
+  if (!RCTValidateTypeOfViewCommandArgument(arg0, [NSNumber class], @"boolean", @"Switch", commandName, @"1st")) {
+    return;
+  }
+#endif
+  BOOL value = [(NSNumber *)arg0 boolValue];
+
+  [componentView setValue:value];
+  return;
+}
+
+#if RCT_DEBUG
+  RCTLogError(@"%@ received command %@, which is not a supported command.", @"Switch", commandName);
+#endif
+}
+
+@protocol RCTInputAccessoryViewProtocol <NSObject>
+
+@end
+
+@protocol RCTUnimplementedNativeViewViewProtocol <NSObject>
+
+@end
+
+@protocol RCTModalHostViewViewProtocol <NSObject>
+
+@end
+
+NS_ASSUME_NONNULL_END
\ No newline at end of file
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp
new file mode 100644
index 0000000..7255b0b
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp
@@ -0,0 +1,29 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateShadowNodeCpp.js
+ */
+
+#include <react/renderer/components/rncore/ShadowNodes.h>
+
+namespace facebook {
+namespace react {
+
+extern const char ActivityIndicatorViewComponentName[] = "ActivityIndicatorView";
+extern const char DatePickerComponentName[] = "DatePicker";
+extern const char AndroidDrawerLayoutComponentName[] = "AndroidDrawerLayout";
+extern const char RCTMaskedViewComponentName[] = "RCTMaskedView";
+extern const char RCTProgressViewComponentName[] = "RCTProgressView";
+extern const char AndroidSwipeRefreshLayoutComponentName[] = "AndroidSwipeRefreshLayout";
+extern const char PullToRefreshViewComponentName[] = "PullToRefreshView";
+extern const char AndroidHorizontalScrollContentViewComponentName[] = "AndroidHorizontalScrollContentView";
+extern const char RCTSegmentedControlComponentName[] = "RCTSegmentedControl";
+extern const char SwitchComponentName[] = "Switch";
+extern const char UnimplementedNativeViewComponentName[] = "UnimplementedNativeView";
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp.bak b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp.bak
new file mode 100644
index 0000000..c544296
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.cpp.bak
@@ -0,0 +1,29 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateShadowNodeCpp.js
+ */
+
+#include <react/renderer/components/FBReactNativeSpec/ShadowNodes.h>
+
+namespace facebook {
+namespace react {
+
+extern const char ActivityIndicatorViewComponentName[] = "ActivityIndicatorView";
+extern const char DatePickerComponentName[] = "DatePicker";
+extern const char AndroidDrawerLayoutComponentName[] = "AndroidDrawerLayout";
+extern const char RCTMaskedViewComponentName[] = "RCTMaskedView";
+extern const char RCTProgressViewComponentName[] = "RCTProgressView";
+extern const char AndroidSwipeRefreshLayoutComponentName[] = "AndroidSwipeRefreshLayout";
+extern const char PullToRefreshViewComponentName[] = "PullToRefreshView";
+extern const char AndroidHorizontalScrollContentViewComponentName[] = "AndroidHorizontalScrollContentView";
+extern const char RCTSegmentedControlComponentName[] = "RCTSegmentedControl";
+extern const char SwitchComponentName[] = "Switch";
+extern const char UnimplementedNativeViewComponentName[] = "UnimplementedNativeView";
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h
new file mode 100644
index 0000000..37278a6
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h
@@ -0,0 +1,126 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateShadowNodeH.js
+ */
+
+#pragma once
+
+#include <react/renderer/components/rncore/EventEmitters.h>
+#include <react/renderer/components/rncore/Props.h>
+#include <react/renderer/components/view/ConcreteViewShadowNode.h>
+
+namespace facebook {
+namespace react {
+
+extern const char ActivityIndicatorViewComponentName[];
+
+/*
+ * `ShadowNode` for <ActivityIndicatorView> component.
+ */
+using ActivityIndicatorViewShadowNode = ConcreteViewShadowNode<
+    ActivityIndicatorViewComponentName,
+    ActivityIndicatorViewProps>;
+
+extern const char DatePickerComponentName[];
+
+/*
+ * `ShadowNode` for <DatePicker> component.
+ */
+using DatePickerShadowNode = ConcreteViewShadowNode<
+    DatePickerComponentName,
+    DatePickerProps,
+DatePickerEventEmitter>;
+
+extern const char AndroidDrawerLayoutComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidDrawerLayout> component.
+ */
+using AndroidDrawerLayoutShadowNode = ConcreteViewShadowNode<
+    AndroidDrawerLayoutComponentName,
+    AndroidDrawerLayoutProps,
+AndroidDrawerLayoutEventEmitter>;
+
+extern const char RCTMaskedViewComponentName[];
+
+/*
+ * `ShadowNode` for <RCTMaskedView> component.
+ */
+using RCTMaskedViewShadowNode = ConcreteViewShadowNode<
+    RCTMaskedViewComponentName,
+    RCTMaskedViewProps>;
+
+extern const char RCTProgressViewComponentName[];
+
+/*
+ * `ShadowNode` for <RCTProgressView> component.
+ */
+using RCTProgressViewShadowNode = ConcreteViewShadowNode<
+    RCTProgressViewComponentName,
+    RCTProgressViewProps>;
+
+extern const char AndroidSwipeRefreshLayoutComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidSwipeRefreshLayout> component.
+ */
+using AndroidSwipeRefreshLayoutShadowNode = ConcreteViewShadowNode<
+    AndroidSwipeRefreshLayoutComponentName,
+    AndroidSwipeRefreshLayoutProps,
+AndroidSwipeRefreshLayoutEventEmitter>;
+
+extern const char PullToRefreshViewComponentName[];
+
+/*
+ * `ShadowNode` for <PullToRefreshView> component.
+ */
+using PullToRefreshViewShadowNode = ConcreteViewShadowNode<
+    PullToRefreshViewComponentName,
+    PullToRefreshViewProps,
+PullToRefreshViewEventEmitter>;
+
+extern const char AndroidHorizontalScrollContentViewComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidHorizontalScrollContentView> component.
+ */
+using AndroidHorizontalScrollContentViewShadowNode = ConcreteViewShadowNode<
+    AndroidHorizontalScrollContentViewComponentName,
+    AndroidHorizontalScrollContentViewProps>;
+
+extern const char RCTSegmentedControlComponentName[];
+
+/*
+ * `ShadowNode` for <RCTSegmentedControl> component.
+ */
+using RCTSegmentedControlShadowNode = ConcreteViewShadowNode<
+    RCTSegmentedControlComponentName,
+    RCTSegmentedControlProps,
+RCTSegmentedControlEventEmitter>;
+
+extern const char SwitchComponentName[];
+
+/*
+ * `ShadowNode` for <Switch> component.
+ */
+using SwitchShadowNode = ConcreteViewShadowNode<
+    SwitchComponentName,
+    SwitchProps,
+SwitchEventEmitter>;
+
+extern const char UnimplementedNativeViewComponentName[];
+
+/*
+ * `ShadowNode` for <UnimplementedNativeView> component.
+ */
+using UnimplementedNativeViewShadowNode = ConcreteViewShadowNode<
+    UnimplementedNativeViewComponentName,
+    UnimplementedNativeViewProps>;
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h.bak b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h.bak
new file mode 100644
index 0000000..402f10a
--- /dev/null
+++ b/node_modules/react-native/ReactCommon/react/renderer/components/rncore/ShadowNodes.h.bak
@@ -0,0 +1,126 @@
+
+/**
+ * Copyright (c) Facebook, Inc. and its affiliates.
+ *
+ * This source code is licensed under the MIT license found in the
+ * LICENSE file in the root directory of this source tree.
+ *
+ * @generated by codegen project: GenerateShadowNodeH.js
+ */
+
+#pragma once
+
+#include <react/renderer/components/FBReactNativeSpec/EventEmitters.h>
+#include <react/renderer/components/FBReactNativeSpec/Props.h>
+#include <react/renderer/components/view/ConcreteViewShadowNode.h>
+
+namespace facebook {
+namespace react {
+
+extern const char ActivityIndicatorViewComponentName[];
+
+/*
+ * `ShadowNode` for <ActivityIndicatorView> component.
+ */
+using ActivityIndicatorViewShadowNode = ConcreteViewShadowNode<
+    ActivityIndicatorViewComponentName,
+    ActivityIndicatorViewProps>;
+
+extern const char DatePickerComponentName[];
+
+/*
+ * `ShadowNode` for <DatePicker> component.
+ */
+using DatePickerShadowNode = ConcreteViewShadowNode<
+    DatePickerComponentName,
+    DatePickerProps,
+DatePickerEventEmitter>;
+
+extern const char AndroidDrawerLayoutComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidDrawerLayout> component.
+ */
+using AndroidDrawerLayoutShadowNode = ConcreteViewShadowNode<
+    AndroidDrawerLayoutComponentName,
+    AndroidDrawerLayoutProps,
+AndroidDrawerLayoutEventEmitter>;
+
+extern const char RCTMaskedViewComponentName[];
+
+/*
+ * `ShadowNode` for <RCTMaskedView> component.
+ */
+using RCTMaskedViewShadowNode = ConcreteViewShadowNode<
+    RCTMaskedViewComponentName,
+    RCTMaskedViewProps>;
+
+extern const char RCTProgressViewComponentName[];
+
+/*
+ * `ShadowNode` for <RCTProgressView> component.
+ */
+using RCTProgressViewShadowNode = ConcreteViewShadowNode<
+    RCTProgressViewComponentName,
+    RCTProgressViewProps>;
+
+extern const char AndroidSwipeRefreshLayoutComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidSwipeRefreshLayout> component.
+ */
+using AndroidSwipeRefreshLayoutShadowNode = ConcreteViewShadowNode<
+    AndroidSwipeRefreshLayoutComponentName,
+    AndroidSwipeRefreshLayoutProps,
+AndroidSwipeRefreshLayoutEventEmitter>;
+
+extern const char PullToRefreshViewComponentName[];
+
+/*
+ * `ShadowNode` for <PullToRefreshView> component.
+ */
+using PullToRefreshViewShadowNode = ConcreteViewShadowNode<
+    PullToRefreshViewComponentName,
+    PullToRefreshViewProps,
+PullToRefreshViewEventEmitter>;
+
+extern const char AndroidHorizontalScrollContentViewComponentName[];
+
+/*
+ * `ShadowNode` for <AndroidHorizontalScrollContentView> component.
+ */
+using AndroidHorizontalScrollContentViewShadowNode = ConcreteViewShadowNode<
+    AndroidHorizontalScrollContentViewComponentName,
+    AndroidHorizontalScrollContentViewProps>;
+
+extern const char RCTSegmentedControlComponentName[];
+
+/*
+ * `ShadowNode` for <RCTSegmentedControl> component.
+ */
+using RCTSegmentedControlShadowNode = ConcreteViewShadowNode<
+    RCTSegmentedControlComponentName,
+    RCTSegmentedControlProps,
+RCTSegmentedControlEventEmitter>;
+
+extern const char SwitchComponentName[];
+
+/*
+ * `ShadowNode` for <Switch> component.
+ */
+using SwitchShadowNode = ConcreteViewShadowNode<
+    SwitchComponentName,
+    SwitchProps,
+SwitchEventEmitter>;
+
+extern const char UnimplementedNativeViewComponentName[];
+
+/*
+ * `ShadowNode` for <UnimplementedNativeView> component.
+ */
+using UnimplementedNativeViewShadowNode = ConcreteViewShadowNode<
+    UnimplementedNativeViewComponentName,
+    UnimplementedNativeViewProps>;
+
+} // namespace react
+} // namespace facebook
diff --git a/node_modules/react-native/ReactCommon/yoga/yoga/Yoga.cpp b/node_modules/react-native/ReactCommon/yoga/yoga/Yoga.cpp
index 2c68674..986dc71 100644
--- a/node_modules/react-native/ReactCommon/yoga/yoga/Yoga.cpp
+++ b/node_modules/react-native/ReactCommon/yoga/yoga/Yoga.cpp
@@ -2229,7 +2229,7 @@ static float YGDistributeFreeSpaceSecondPass(
         depth,
         generationCount);
     node->setLayoutHadOverflow(
-        node->getLayout().hadOverflow() |
+                               node->getLayout().hadOverflow() ||
         currentRelativeChild->getLayout().hadOverflow());
   }
   return deltaFreeSpace;
diff --git a/node_modules/react-native/scripts/.packager.env b/node_modules/react-native/scripts/.packager.env
new file mode 100644
index 0000000..361f5fb
--- /dev/null
+++ b/node_modules/react-native/scripts/.packager.env
@@ -0,0 +1 @@
+export RCT_METRO_PORT=8081
