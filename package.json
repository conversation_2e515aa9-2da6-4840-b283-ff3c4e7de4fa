{"name": "reactnativebridgeios", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "postinstall": "patch-package"}, "dependencies": {"@react-native-community/viewpager": "^5.0.11", "patch-package": "^8.0.0", "react": "17.0.2", "react-native": "0.66.0", "react-native-dropdown-select-list": "^2.0.5", "react-native-gesture-handler": "^2.13.0", "react-native-safe-area-context": "^3.3.2", "react-native-screens": "^3.11.0", "react-navigation": "^4.4.4", "react-navigation-stack": "^2.10.4"}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@react-native-community/eslint-config": "^2.0.0", "babel-jest": "^26.6.3", "eslint": "7.14.0", "jest": "^26.6.3", "metro-react-native-babel-preset": "^0.66.2", "react-test-renderer": "17.0.2"}, "jest": {"preset": "react-native"}}