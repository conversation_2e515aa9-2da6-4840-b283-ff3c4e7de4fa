{"name": "reactnativebridgeios", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "postinstall": "patch-package"}, "dependencies": {"@react-native-community/viewpager": "^5.0.11", "patch-package": "^8.0.0", "react": "18.1.0", "react-native": "0.70.15", "react-native-dropdown-select-list": "^2.0.5", "react-native-gesture-handler": "~2.8.0", "react-native-safe-area-context": "^4.4.1", "react-native-screens": "~3.18.2", "react-navigation": "^4.4.4", "react-navigation-stack": "^2.10.4"}, "devDependencies": {"@babel/core": "^7.25.0", "@babel/preset-env": "^7.25.0", "@babel/runtime": "^7.25.0", "@react-native-community/eslint-config": "^3.2.0", "babel-jest": "^29.7.0", "eslint": "^8.19.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "0.73.9", "react-test-renderer": "18.1.0"}, "jest": {"preset": "react-native"}}