PODS:
  - boost (1.76.0)
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.66.0)
  - FBReactNativeSpec (0.66.0):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.66.0)
    - RCTTypeSafety (= 0.66.0)
    - React-Core (= 0.66.0)
    - React-jsi (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - Flipper (0.99.0):
    - Flipper-Folly (~> 2.6)
    - Flipper-RSocket (~> 1.4)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (3.1.7)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.7):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.180)
  - Flipper-Glog (0.3.6)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.99.0):
    - FlipperKit/Core (= 0.99.0)
  - FlipperKit/Core (0.99.0):
    - Flipper (~> 0.99.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
  - FlipperKit/CppBridge (0.99.0):
    - Flipper (~> 0.99.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.99.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.99.0)
  - FlipperKit/FKPortForwarding (0.99.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.99.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.99.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.99.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.99.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.99.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.99.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.99.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.99.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.99.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - libevent (2.1.12)
  - OpenSSL-Universal (1.1.180)
  - RCT-Folly (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.06.28.00-v2)
  - RCT-Folly/Default (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.66.0)
  - RCTTypeSafety (0.66.0):
    - FBLazyVector (= 0.66.0)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.66.0)
    - React-Core (= 0.66.0)
  - React (0.66.0):
    - React-Core (= 0.66.0)
    - React-Core/DevSupport (= 0.66.0)
    - React-Core/RCTWebSocket (= 0.66.0)
    - React-RCTActionSheet (= 0.66.0)
    - React-RCTAnimation (= 0.66.0)
    - React-RCTBlob (= 0.66.0)
    - React-RCTImage (= 0.66.0)
    - React-RCTLinking (= 0.66.0)
    - React-RCTNetwork (= 0.66.0)
    - React-RCTSettings (= 0.66.0)
    - React-RCTText (= 0.66.0)
    - React-RCTVibration (= 0.66.0)
  - React-callinvoker (0.66.0)
  - React-Core (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.66.0)
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/CoreModulesHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/Default (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/DevSupport (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.66.0)
    - React-Core/RCTWebSocket (= 0.66.0)
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-jsinspector (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTBlobHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTImageHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTTextHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-Core/RCTWebSocket (0.66.0):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.66.0)
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsiexecutor (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - Yoga
  - React-CoreModules (0.66.0):
    - FBReactNativeSpec (= 0.66.0)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.0)
    - React-Core/CoreModulesHeaders (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-RCTImage (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - React-cxxreact (0.66.0):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-jsinspector (= 0.66.0)
    - React-logger (= 0.66.0)
    - React-perflogger (= 0.66.0)
    - React-runtimeexecutor (= 0.66.0)
  - React-jsi (0.66.0):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-jsi/Default (= 0.66.0)
  - React-jsi/Default (0.66.0):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
  - React-jsiexecutor (0.66.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-perflogger (= 0.66.0)
  - React-jsinspector (0.66.0)
  - React-logger (0.66.0):
    - glog
  - react-native-safe-area-context (3.3.2):
    - React-Core
  - react-native-viewpager (5.0.11):
    - React-Core
  - React-perflogger (0.66.0)
  - React-RCTActionSheet (0.66.0):
    - React-Core/RCTActionSheetHeaders (= 0.66.0)
  - React-RCTAnimation (0.66.0):
    - FBReactNativeSpec (= 0.66.0)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.0)
    - React-Core/RCTAnimationHeaders (= 0.66.0)
    - React-jsi (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - React-RCTBlob (0.66.0):
    - FBReactNativeSpec (= 0.66.0)
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/RCTBlobHeaders (= 0.66.0)
    - React-Core/RCTWebSocket (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-RCTNetwork (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - React-RCTImage (0.66.0):
    - FBReactNativeSpec (= 0.66.0)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.0)
    - React-Core/RCTImageHeaders (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-RCTNetwork (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - React-RCTLinking (0.66.0):
    - FBReactNativeSpec (= 0.66.0)
    - React-Core/RCTLinkingHeaders (= 0.66.0)
    - React-jsi (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - React-RCTNetwork (0.66.0):
    - FBReactNativeSpec (= 0.66.0)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.0)
    - React-Core/RCTNetworkHeaders (= 0.66.0)
    - React-jsi (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - React-RCTSettings (0.66.0):
    - FBReactNativeSpec (= 0.66.0)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.66.0)
    - React-Core/RCTSettingsHeaders (= 0.66.0)
    - React-jsi (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - React-RCTText (0.66.0):
    - React-Core/RCTTextHeaders (= 0.66.0)
  - React-RCTVibration (0.66.0):
    - FBReactNativeSpec (= 0.66.0)
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/RCTVibrationHeaders (= 0.66.0)
    - React-jsi (= 0.66.0)
    - ReactCommon/turbomodule/core (= 0.66.0)
  - React-runtimeexecutor (0.66.0):
    - React-jsi (= 0.66.0)
  - ReactCommon/turbomodule/core (0.66.0):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.66.0)
    - React-Core (= 0.66.0)
    - React-cxxreact (= 0.66.0)
    - React-jsi (= 0.66.0)
    - React-logger (= 0.66.0)
    - React-perflogger (= 0.66.0)
  - RNGestureHandler (2.13.0):
    - React-Core
  - RNScreens (3.11.0):
    - React-Core
    - React-RCTImage
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.99.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= 3.1.7)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.7)
  - Flipper-Glog (= 0.3.6)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.99.0)
  - FlipperKit/Core (= 0.99.0)
  - FlipperKit/CppBridge (= 0.99.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.99.0)
  - FlipperKit/FBDefines (= 0.99.0)
  - FlipperKit/FKPortForwarding (= 0.99.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.99.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.99.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.99.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.99.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.99.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.99.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.99.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - "react-native-viewpager (from `../node_modules/@react-native-community/viewpager`)"
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - libevent
    - OpenSSL-Universal
    - YogaKit

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-viewpager:
    :path: "../node_modules/@react-native-community/viewpager"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: a7c83b31436843459a1961bfd74b96033dc77234
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 831926d9b8bf8166fd87886c4abab286c2422662
  FBLazyVector: 6816ca39e1cc8beffd2a96783f518296789d1c48
  FBReactNativeSpec: 3b1e86618e902743fde35b40cf9ebd100fd655b7
  Flipper: 30e8eeeed6abdc98edaf32af0cda2f198be4b733
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 57ffbe81ef95306cc9e69c4aa3aeeeeb58a6a28c
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 83af37379faa69497529e414bd43fbfc7cae259a
  Flipper-Glog: 1dfd6abf1e922806c52ceb8701a3599a79a200a6
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: d8d346844eca5d9120c17d441a2f38596e8ed2b9
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 5337263514dd6f09803962437687240c5dc39aa4
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  OpenSSL-Universal: 1aa4f6a6ee7256b83db99ec1ccdaa80d10f9af9b
  RCT-Folly: a21c126816d8025b547704b777a2ba552f3d9fa9
  RCTRequired: e4a18a90004e0ed97bba9081099104fd0f658dc9
  RCTTypeSafety: 8a3c31d38de58e1a6a7df6e4e643644a60b00e22
  React: 2b1d0dc3c23e01b754588a74a5b265282d9eb61e
  React-callinvoker: 57c195e780695285fa56e61efdbc0ca0e9204484
  React-Core: 45e4b3c57b0b5fdbb24bc6a63a964870c0405955
  React-CoreModules: d7bb1ae3436eddd85a7eb6d5e928f8c1655d87db
  React-cxxreact: 60c850e9997b21ee302757c36a460efc944183e7
  React-jsi: 38d68cb1b53843703100830d530342b32f8e0878
  React-jsiexecutor: 6a05173dc0142abc582bd4edd2d23146b8cc218a
  React-jsinspector: be95ad424ba9f7b817aff22732eb9b1b810a000a
  React-logger: 9a9cd87d4ea681ae929b32ef580638ff1b50fb24
  react-native-safe-area-context: 584dc04881deb49474363f3be89e4ca0e854c057
  react-native-viewpager: b99b53127d830885917ef84809c5065edd614a78
  React-perflogger: 1f554c2b684e2f484f9edcdfdaeedab039fbaca8
  React-RCTActionSheet: 610d5a5d71ab4808734782c8bca6a12ec3563672
  React-RCTAnimation: ec6ed97370ace32724c253f29f0586cafcab8126
  React-RCTBlob: b3270d498ff240f49c50e1bc950b6e5fd48886ba
  React-RCTImage: 23d5e26669b31230bea3fd99eb703af699e5d61a
  React-RCTLinking: edaaee9dee82b79e90e7b903d8913fa72284fbba
  React-RCTNetwork: e8825053dd1b5c2a0e1aa3cf1127750b624f90c0
  React-RCTSettings: 40d7ae987031c5dc561d11cd3a15cc1245a11d42
  React-RCTText: 6e104479d4f0bb593b4cf90b6fc6e5390c12ccde
  React-RCTVibration: 53b92d54b923283638cb0186da7a5c2d2b70a49b
  React-runtimeexecutor: 4bb657a97aa74568d9ed634c8bd478299bb8a3a6
  ReactCommon: eb059748e842a1a86025ebbd4ac9d99e74492f88
  RNGestureHandler: 6e4dc6b7ab3a385386d4e36228bd065e5a611394
  RNScreens: fd535547baa4ef8aeaee1a8b1e3ffd17b8df44a4
  Yoga: c11abbf5809216c91fcd62f5571078b83d9b6720
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: eb317001c76df154e21b084a7169594ba559e0ed

COCOAPODS: 1.15.2
